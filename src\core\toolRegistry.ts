import * as vscode from 'vscode';
import { AgentAction } from './augmentAgent';

export interface Tool {
    name: string;
    description: string;
    parameters: ToolParameter[];
    execute: (action: AgentAction) => Promise<any>;
}

export interface ToolParameter {
    name: string;
    type: 'string' | 'number' | 'boolean' | 'object' | 'array';
    description: string;
    required: boolean;
    default?: any;
}

export interface ToolResult {
    success: boolean;
    result?: any;
    error?: string;
    metadata?: any;
}

export class ToolRegistry {
    private tools = new Map<string, Tool>();

    constructor() {
        this.registerBuiltInTools();
    }

    register(name: string, execute: (action: AgentAction) => Promise<any>, description?: string, parameters?: ToolParameter[]): void {
        const tool: Tool = {
            name,
            description: description || `Execute ${name} action`,
            parameters: parameters || [],
            execute
        };

        this.tools.set(name, tool);
    }

    async execute(toolName: string, action: AgentAction): Promise<ToolResult> {
        const tool = this.tools.get(toolName);
        if (!tool) {
            return {
                success: false,
                error: `Tool '${toolName}' not found`
            };
        }

        try {
            const result = await tool.execute(action);
            return {
                success: true,
                result,
                metadata: {
                    tool: toolName,
                    timestamp: new Date(),
                    action: action.type
                }
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error),
                metadata: {
                    tool: toolName,
                    timestamp: new Date(),
                    action: action.type
                }
            };
        }
    }

    getTools(): Tool[] {
        return Array.from(this.tools.values());
    }

    getTool(name: string): Tool | undefined {
        return this.tools.get(name);
    }

    private registerBuiltInTools(): void {
        // File operations
        this.register(
            'create_file',
            this.createFile.bind(this),
            'Create a new file with specified content',
            [
                { name: 'target', type: 'string', description: 'File path', required: true },
                { name: 'content', type: 'string', description: 'File content', required: false, default: '' }
            ]
        );

        this.register(
            'modify_file',
            this.modifyFile.bind(this),
            'Modify an existing file',
            [
                { name: 'target', type: 'string', description: 'File path', required: true },
                { name: 'content', type: 'string', description: 'New content or changes', required: true }
            ]
        );

        this.register(
            'delete_file',
            this.deleteFile.bind(this),
            'Delete a file',
            [
                { name: 'target', type: 'string', description: 'File path', required: true }
            ]
        );

        this.register(
            'read_file',
            this.readFile.bind(this),
            'Read file content',
            [
                { name: 'target', type: 'string', description: 'File path', required: true }
            ]
        );

        // Terminal operations
        this.register(
            'run_command',
            this.runCommand.bind(this),
            'Execute a terminal command',
            [
                { name: 'target', type: 'string', description: 'Command to execute', required: true }
            ]
        );

        // Code operations
        this.register(
            'apply_code',
            this.applyCode.bind(this),
            'Apply code to current cursor position',
            [
                { name: 'content', type: 'string', description: 'Code to apply', required: true }
            ]
        );

        this.register(
            'format_code',
            this.formatCode.bind(this),
            'Format code in the active editor',
            []
        );

        // Search operations
        this.register(
            'search_files',
            this.searchFiles.bind(this),
            'Search for text across files',
            [
                { name: 'target', type: 'string', description: 'Search query', required: true }
            ]
        );

        this.register(
            'find_references',
            this.findReferences.bind(this),
            'Find references to a symbol',
            [
                { name: 'target', type: 'string', description: 'Symbol name', required: true }
            ]
        );

        // Git operations
        this.register(
            'git_status',
            this.gitStatus.bind(this),
            'Get Git status',
            []
        );

        this.register(
            'git_commit',
            this.gitCommit.bind(this),
            'Create a Git commit',
            [
                { name: 'target', type: 'string', description: 'Commit message', required: true }
            ]
        );
    }

    // Tool implementations
    private async createFile(action: AgentAction): Promise<any> {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder open');
        }

        const filePath = vscode.Uri.joinPath(workspaceFolder.uri, action.target);
        const content = action.content || '';

        // Ensure directory exists
        const dirPath = vscode.Uri.joinPath(filePath, '..');
        await vscode.workspace.fs.createDirectory(dirPath);

        // Write file
        await vscode.workspace.fs.writeFile(filePath, Buffer.from(content, 'utf8'));

        // Open the created file
        const document = await vscode.workspace.openTextDocument(filePath);
        await vscode.window.showTextDocument(document);

        return {
            path: action.target,
            size: content.length,
            created: true
        };
    }

    private async modifyFile(action: AgentAction): Promise<any> {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder open');
        }

        const filePath = vscode.Uri.joinPath(workspaceFolder.uri, action.target);
        
        // Check if file exists
        try {
            await vscode.workspace.fs.stat(filePath);
        } catch (error) {
            throw new Error(`File ${action.target} does not exist`);
        }

        // Read current content
        const currentContent = await vscode.workspace.fs.readFile(filePath);
        const currentText = currentContent.toString();

        // Apply modification (this is a simple replacement, could be enhanced)
        const newContent = action.content || '';
        
        // Write modified content
        await vscode.workspace.fs.writeFile(filePath, Buffer.from(newContent, 'utf8'));

        // Refresh the editor if it's open
        const document = await vscode.workspace.openTextDocument(filePath);
        await vscode.window.showTextDocument(document);

        return {
            path: action.target,
            oldSize: currentText.length,
            newSize: newContent.length,
            modified: true
        };
    }

    private async deleteFile(action: AgentAction): Promise<any> {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder open');
        }

        const filePath = vscode.Uri.joinPath(workspaceFolder.uri, action.target);
        
        // Check if file exists
        try {
            const stat = await vscode.workspace.fs.stat(filePath);
            await vscode.workspace.fs.delete(filePath);
            
            return {
                path: action.target,
                size: stat.size,
                deleted: true
            };
        } catch (error) {
            throw new Error(`File ${action.target} does not exist or cannot be deleted`);
        }
    }

    private async readFile(action: AgentAction): Promise<any> {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder open');
        }

        const filePath = vscode.Uri.joinPath(workspaceFolder.uri, action.target);
        
        try {
            const content = await vscode.workspace.fs.readFile(filePath);
            const text = content.toString();
            
            return {
                path: action.target,
                content: text,
                size: text.length,
                lines: text.split('\n').length
            };
        } catch (error) {
            throw new Error(`Cannot read file ${action.target}`);
        }
    }

    private async runCommand(action: AgentAction): Promise<any> {
        const terminal = vscode.window.createTerminal({
            name: 'Augment Agent',
            cwd: vscode.workspace.workspaceFolders?.[0]?.uri.fsPath
        });

        terminal.show();
        terminal.sendText(action.target);

        return {
            command: action.target,
            terminal: terminal.name,
            executed: true
        };
    }

    private async applyCode(action: AgentAction): Promise<any> {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            throw new Error('No active editor');
        }

        const position = editor.selection.active;
        const code = action.content || '';

        await editor.edit(editBuilder => {
            editBuilder.insert(position, code);
        });

        return {
            position: {
                line: position.line,
                character: position.character
            },
            code: code,
            applied: true
        };
    }

    private async formatCode(action: AgentAction): Promise<any> {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            throw new Error('No active editor');
        }

        await vscode.commands.executeCommand('editor.action.formatDocument');

        return {
            document: editor.document.fileName,
            formatted: true
        };
    }

    private async searchFiles(action: AgentAction): Promise<any> {
        const query = action.target;
        const results: any[] = [];

        // Use VS Code's workspace search
        try {
            const files = await vscode.workspace.findFiles('**/*', '**/node_modules/**');

            for (const file of files.slice(0, 100)) { // Limit to first 100 files
                try {
                    const content = await vscode.workspace.fs.readFile(file);
                    const text = content.toString();
                    const lines = text.split('\n');

                    const matches: any[] = [];
                    lines.forEach((line, index) => {
                        if (line.toLowerCase().includes(query.toLowerCase())) {
                            matches.push({
                                line: index + 1,
                                text: line.trim(),
                                preview: line.trim()
                            });
                        }
                    });

                    if (matches.length > 0) {
                        results.push({
                            file: vscode.workspace.asRelativePath(file),
                            matches
                        });
                    }
                } catch (error) {
                    // Skip files that can't be read
                }
            }
        } catch (error) {
            console.error('Search error:', error);
        }

        return {
            query,
            results,
            totalMatches: results.reduce((sum, file) => sum + file.matches.length, 0)
        };
    }

    private async findReferences(action: AgentAction): Promise<any> {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            throw new Error('No active editor');
        }

        const position = editor.selection.active;
        const locations = await vscode.commands.executeCommand<vscode.Location[]>(
            'vscode.executeReferenceProvider',
            editor.document.uri,
            position
        );

        const references = locations?.map(location => ({
            file: vscode.workspace.asRelativePath(location.uri),
            line: location.range.start.line + 1,
            character: location.range.start.character + 1
        })) || [];

        return {
            symbol: action.target,
            references,
            count: references.length
        };
    }

    private async gitStatus(action: AgentAction): Promise<any> {
        const terminal = vscode.window.createTerminal({
            name: 'Git Status',
            cwd: vscode.workspace.workspaceFolders?.[0]?.uri.fsPath
        });

        terminal.sendText('git status --porcelain');
        
        return {
            command: 'git status',
            executed: true
        };
    }

    private async gitCommit(action: AgentAction): Promise<any> {
        const message = action.target;
        const terminal = vscode.window.createTerminal({
            name: 'Git Commit',
            cwd: vscode.workspace.workspaceFolders?.[0]?.uri.fsPath
        });

        terminal.sendText(`git add . && git commit -m "${message}"`);
        
        return {
            message,
            executed: true
        };
    }
}
