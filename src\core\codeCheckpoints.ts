import * as vscode from 'vscode';
import * as path from 'path';
import * as crypto from 'crypto';

export interface Checkpoint {
    id: string;
    timestamp: Date;
    description: string;
    files: CheckpointFile[];
    workspaceState: any;
    gitCommit?: string;
    agentAction?: string;
}

export interface CheckpointFile {
    path: string;
    content: string;
    hash: string;
    language: string;
    size: number;
}

export interface CheckpointDiff {
    file: string;
    changes: FileDiff[];
}

export interface FileDiff {
    type: 'insert' | 'delete' | 'modify';
    lineNumber: number;
    oldContent?: string;
    newContent?: string;
    context: string[];
}

export class CodeCheckpoints {
    private checkpoints: Map<string, Checkpoint> = new Map();
    private maxCheckpoints = 50;
    private checkpointDir: vscode.Uri;
    private currentCheckpoint?: string;

    constructor(private context: vscode.ExtensionContext) {
        this.checkpointDir = vscode.Uri.joinPath(context.globalStorageUri, 'checkpoints');
        this.loadCheckpoints();
    }

    async create(description: string, agentAction?: string): Promise<string> {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder open');
        }

        const checkpointId = this.generateCheckpointId();
        const files = await this.captureWorkspaceFiles(workspaceFolder.uri);
        const workspaceState = await this.captureWorkspaceState();
        const gitCommit = await this.getCurrentGitCommit();

        const checkpoint: Checkpoint = {
            id: checkpointId,
            timestamp: new Date(),
            description,
            files,
            workspaceState,
            gitCommit,
            agentAction
        };

        this.checkpoints.set(checkpointId, checkpoint);
        this.currentCheckpoint = checkpointId;

        // Clean up old checkpoints if we exceed the limit
        await this.cleanupOldCheckpoints();

        // Save checkpoint to disk
        await this.saveCheckpoint(checkpoint);
        await this.saveCheckpointIndex();

        return checkpointId;
    }

    async restore(checkpointId: string): Promise<void> {
        const checkpoint = this.checkpoints.get(checkpointId);
        if (!checkpoint) {
            throw new Error(`Checkpoint ${checkpointId} not found`);
        }

        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder open');
        }

        // Show confirmation dialog
        const choice = await vscode.window.showWarningMessage(
            `Are you sure you want to restore to checkpoint "${checkpoint.description}"? This will overwrite current changes.`,
            { modal: true },
            'Restore',
            'Cancel'
        );

        if (choice !== 'Restore') {
            return;
        }

        // Create a backup checkpoint before restoring
        await this.create('Before restore', `Restoring to: ${checkpoint.description}`);

        // Restore files
        for (const file of checkpoint.files) {
            const filePath = vscode.Uri.joinPath(workspaceFolder.uri, file.path);
            
            try {
                // Ensure directory exists
                const dirPath = vscode.Uri.joinPath(filePath, '..');
                await vscode.workspace.fs.createDirectory(dirPath);
                
                // Write file content
                await vscode.workspace.fs.writeFile(filePath, Buffer.from(file.content, 'utf8'));
            } catch (error) {
                console.error(`Failed to restore file ${file.path}:`, error);
            }
        }

        // Restore workspace state if possible
        await this.restoreWorkspaceState(checkpoint.workspaceState);

        this.currentCheckpoint = checkpointId;

        vscode.window.showInformationMessage(`Restored to checkpoint: ${checkpoint.description}`);
    }

    async compare(checkpointId1: string, checkpointId2?: string): Promise<CheckpointDiff[]> {
        const checkpoint1 = this.checkpoints.get(checkpointId1);
        if (!checkpoint1) {
            throw new Error(`Checkpoint ${checkpointId1} not found`);
        }

        let checkpoint2: Checkpoint;
        if (checkpointId2) {
            const cp2 = this.checkpoints.get(checkpointId2);
            if (!cp2) {
                throw new Error(`Checkpoint ${checkpointId2} not found`);
            }
            checkpoint2 = cp2;
        } else {
            // Compare with current workspace state
            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            if (!workspaceFolder) {
                throw new Error('No workspace folder open');
            }
            
            const currentFiles = await this.captureWorkspaceFiles(workspaceFolder.uri);
            checkpoint2 = {
                id: 'current',
                timestamp: new Date(),
                description: 'Current state',
                files: currentFiles,
                workspaceState: {}
            };
        }

        return this.generateDiff(checkpoint1, checkpoint2);
    }

    private async captureWorkspaceFiles(workspaceUri: vscode.Uri): Promise<CheckpointFile[]> {
        const files: CheckpointFile[] = [];
        
        try {
            await this.scanDirectory(workspaceUri, files, 0, 3); // Max depth of 3
        } catch (error) {
            console.error('Error capturing workspace files:', error);
        }

        return files;
    }

    private async scanDirectory(
        dirUri: vscode.Uri, 
        files: CheckpointFile[], 
        currentDepth: number, 
        maxDepth: number
    ): Promise<void> {
        if (currentDepth >= maxDepth) return;

        try {
            const entries = await vscode.workspace.fs.readDirectory(dirUri);
            
            for (const [name, type] of entries) {
                // Skip common ignore patterns
                if (this.shouldIgnoreFile(name)) continue;

                const entryUri = vscode.Uri.joinPath(dirUri, name);
                
                if (type === vscode.FileType.File) {
                    try {
                        const content = await vscode.workspace.fs.readFile(entryUri);
                        const contentStr = content.toString();
                        const relativePath = vscode.workspace.asRelativePath(entryUri);
                        
                        // Only include text files and limit size
                        if (this.isTextFile(name) && contentStr.length < 1024 * 1024) { // 1MB limit
                            files.push({
                                path: relativePath,
                                content: contentStr,
                                hash: this.generateHash(contentStr),
                                language: this.getLanguageFromExtension(name),
                                size: contentStr.length
                            });
                        }
                    } catch (error) {
                        // Skip files that can't be read
                        console.warn(`Skipping file ${name}:`, error);
                    }
                } else if (type === vscode.FileType.Directory) {
                    await this.scanDirectory(entryUri, files, currentDepth + 1, maxDepth);
                }
            }
        } catch (error) {
            console.error(`Error scanning directory ${dirUri.fsPath}:`, error);
        }
    }

    private shouldIgnoreFile(name: string): boolean {
        const ignorePatterns = [
            'node_modules', '.git', '.vscode', 'dist', 'build', 'out',
            '.DS_Store', 'Thumbs.db', '.env', '.env.local',
            '__pycache__', '.pytest_cache', 'venv', 'env',
            'target', 'bin', 'obj', '.vs', '.idea',
            '.next', '.nuxt', 'coverage'
        ];

        return ignorePatterns.some(pattern => name.startsWith(pattern)) ||
               name.endsWith('.log') ||
               name.endsWith('.tmp') ||
               name.endsWith('.cache');
    }

    private isTextFile(filename: string): boolean {
        const textExtensions = [
            '.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cpp', '.c', '.cs',
            '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala',
            '.html', '.css', '.scss', '.sass', '.less', '.json', '.xml',
            '.yaml', '.yml', '.md', '.txt', '.sql', '.sh', '.ps1',
            '.dockerfile', '.gitignore', '.gitattributes', '.editorconfig',
            '.eslintrc', '.prettierrc', '.babelrc', '.npmrc'
        ];

        const ext = path.extname(filename).toLowerCase();
        return textExtensions.includes(ext) || !ext; // Include files without extension
    }

    private getLanguageFromExtension(filename: string): string {
        const ext = path.extname(filename).toLowerCase();
        const languageMap: { [key: string]: string } = {
            '.js': 'javascript',
            '.ts': 'typescript',
            '.jsx': 'javascriptreact',
            '.tsx': 'typescriptreact',
            '.py': 'python',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.cs': 'csharp',
            '.php': 'php',
            '.rb': 'ruby',
            '.go': 'go',
            '.rs': 'rust',
            '.swift': 'swift',
            '.kt': 'kotlin',
            '.scala': 'scala',
            '.html': 'html',
            '.css': 'css',
            '.scss': 'scss',
            '.sass': 'sass',
            '.less': 'less',
            '.json': 'json',
            '.xml': 'xml',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.md': 'markdown',
            '.sql': 'sql',
            '.sh': 'shellscript',
            '.ps1': 'powershell'
        };

        return languageMap[ext] || 'plaintext';
    }

    private generateHash(content: string): string {
        return crypto.createHash('sha256').update(content).digest('hex').substring(0, 16);
    }

    private async captureWorkspaceState(): Promise<any> {
        return {
            openEditors: vscode.window.tabGroups.all.map(group => 
                group.tabs.map(tab => ({
                    uri: tab.input instanceof vscode.TabInputText ? tab.input.uri.toString() : null,
                    isActive: tab.isActive
                }))
            ),
            workspaceConfiguration: {
                // Capture relevant workspace settings
            }
        };
    }

    private async restoreWorkspaceState(state: any): Promise<void> {
        // Restore open editors if possible
        if (state.openEditors) {
            for (const group of state.openEditors) {
                for (const editor of group) {
                    if (editor.uri) {
                        try {
                            const uri = vscode.Uri.parse(editor.uri);
                            const document = await vscode.workspace.openTextDocument(uri);
                            if (editor.isActive) {
                                await vscode.window.showTextDocument(document);
                            }
                        } catch (error) {
                            // Ignore errors when restoring editors
                        }
                    }
                }
            }
        }
    }

    private async getCurrentGitCommit(): Promise<string | undefined> {
        // Implementation for getting current Git commit
        // This would use Git commands to get the current commit hash
        return undefined;
    }

    private generateDiff(checkpoint1: Checkpoint, checkpoint2: Checkpoint): CheckpointDiff[] {
        const diffs: CheckpointDiff[] = [];
        const files1 = new Map(checkpoint1.files.map(f => [f.path, f]));
        const files2 = new Map(checkpoint2.files.map(f => [f.path, f]));

        // Find all unique file paths
        const allPaths = new Set([...files1.keys(), ...files2.keys()]);

        for (const filePath of allPaths) {
            const file1 = files1.get(filePath);
            const file2 = files2.get(filePath);

            if (!file1 && file2) {
                // File was added
                diffs.push({
                    file: filePath,
                    changes: [{
                        type: 'insert',
                        lineNumber: 1,
                        newContent: file2.content,
                        context: []
                    }]
                });
            } else if (file1 && !file2) {
                // File was deleted
                diffs.push({
                    file: filePath,
                    changes: [{
                        type: 'delete',
                        lineNumber: 1,
                        oldContent: file1.content,
                        context: []
                    }]
                });
            } else if (file1 && file2 && file1.hash !== file2.hash) {
                // File was modified
                const fileDiffs = this.generateFileDiff(file1.content, file2.content);
                if (fileDiffs.length > 0) {
                    diffs.push({
                        file: filePath,
                        changes: fileDiffs
                    });
                }
            }
        }

        return diffs;
    }

    private generateFileDiff(content1: string, content2: string): FileDiff[] {
        // Simple line-by-line diff implementation
        const lines1 = content1.split('\n');
        const lines2 = content2.split('\n');
        const diffs: FileDiff[] = [];

        const maxLines = Math.max(lines1.length, lines2.length);
        
        for (let i = 0; i < maxLines; i++) {
            const line1 = lines1[i];
            const line2 = lines2[i];

            if (line1 !== line2) {
                if (line1 === undefined) {
                    diffs.push({
                        type: 'insert',
                        lineNumber: i + 1,
                        newContent: line2,
                        context: this.getContext(lines2, i)
                    });
                } else if (line2 === undefined) {
                    diffs.push({
                        type: 'delete',
                        lineNumber: i + 1,
                        oldContent: line1,
                        context: this.getContext(lines1, i)
                    });
                } else {
                    diffs.push({
                        type: 'modify',
                        lineNumber: i + 1,
                        oldContent: line1,
                        newContent: line2,
                        context: this.getContext(lines2, i)
                    });
                }
            }
        }

        return diffs;
    }

    private getContext(lines: string[], lineIndex: number): string[] {
        const contextSize = 2;
        const start = Math.max(0, lineIndex - contextSize);
        const end = Math.min(lines.length, lineIndex + contextSize + 1);
        return lines.slice(start, end);
    }

    private generateCheckpointId(): string {
        return `cp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    private async cleanupOldCheckpoints(): Promise<void> {
        if (this.checkpoints.size <= this.maxCheckpoints) return;

        // Sort checkpoints by timestamp and keep only the most recent ones
        const sortedCheckpoints = Array.from(this.checkpoints.entries())
            .sort(([, a], [, b]) => b.timestamp.getTime() - a.timestamp.getTime());

        const toDelete = sortedCheckpoints.slice(this.maxCheckpoints);
        
        for (const [id] of toDelete) {
            this.checkpoints.delete(id);
            await this.deleteCheckpointFile(id);
        }
    }

    private async saveCheckpoint(checkpoint: Checkpoint): Promise<void> {
        try {
            await vscode.workspace.fs.createDirectory(this.checkpointDir);
            const checkpointFile = vscode.Uri.joinPath(this.checkpointDir, `${checkpoint.id}.json`);
            await vscode.workspace.fs.writeFile(
                checkpointFile,
                Buffer.from(JSON.stringify(checkpoint, null, 2), 'utf8')
            );
        } catch (error) {
            console.error('Failed to save checkpoint:', error);
        }
    }

    private async saveCheckpointIndex(): Promise<void> {
        try {
            const index = Array.from(this.checkpoints.entries()).map(([id, checkpoint]) => ({
                id,
                timestamp: checkpoint.timestamp,
                description: checkpoint.description,
                agentAction: checkpoint.agentAction,
                fileCount: checkpoint.files.length
            }));

            const indexFile = vscode.Uri.joinPath(this.checkpointDir, 'index.json');
            await vscode.workspace.fs.writeFile(
                indexFile,
                Buffer.from(JSON.stringify(index, null, 2), 'utf8')
            );
        } catch (error) {
            console.error('Failed to save checkpoint index:', error);
        }
    }

    private async deleteCheckpointFile(checkpointId: string): Promise<void> {
        try {
            const checkpointFile = vscode.Uri.joinPath(this.checkpointDir, `${checkpointId}.json`);
            await vscode.workspace.fs.delete(checkpointFile);
        } catch (error) {
            // Ignore errors when deleting checkpoint files
        }
    }

    private async loadCheckpoints(): Promise<void> {
        try {
            const indexFile = vscode.Uri.joinPath(this.checkpointDir, 'index.json');
            const indexContent = await vscode.workspace.fs.readFile(indexFile);
            const index = JSON.parse(indexContent.toString());

            for (const item of index) {
                try {
                    const checkpointFile = vscode.Uri.joinPath(this.checkpointDir, `${item.id}.json`);
                    const checkpointContent = await vscode.workspace.fs.readFile(checkpointFile);
                    const checkpoint = JSON.parse(checkpointContent.toString());
                    checkpoint.timestamp = new Date(checkpoint.timestamp);
                    this.checkpoints.set(item.id, checkpoint);
                } catch (error) {
                    console.warn(`Failed to load checkpoint ${item.id}:`, error);
                }
            }
        } catch (error) {
            // No existing checkpoints, start fresh
            console.log('No existing checkpoints found');
        }
    }

    getCheckpoints(): Checkpoint[] {
        return Array.from(this.checkpoints.values())
            .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
    }

    getCurrentCheckpoint(): string | undefined {
        return this.currentCheckpoint;
    }

    async dispose(): Promise<void> {
        await this.saveCheckpointIndex();
    }
}
