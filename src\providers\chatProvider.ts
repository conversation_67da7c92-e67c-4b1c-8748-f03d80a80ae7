import * as vscode from 'vscode';
import * as path from 'path';
import { OllamaClient } from '../services/ollamaClient';
import { ContextEngine } from '../services/contextEngine';
import { ChatMessage, ChatRequest } from '../types/ollama';

export class Chat<PERSON>rovider implements vscode.TreeDataProvider<ChatTreeItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<ChatTreeItem | undefined | null | void> = new vscode.EventEmitter<ChatTreeItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<ChatTreeItem | undefined | null | void> = this._onDidChangeTreeData.event;

    private chatPanel: vscode.WebviewPanel | undefined;
    private conversationHistory: ChatMessage[] = [];

    constructor(
        private context: vscode.ExtensionContext,
        private ollamaClient: OllamaClient,
        private contextEngine: ContextEngine
    ) {}

    getTreeItem(element: ChatTreeItem): vscode.TreeItem {
        return element;
    }

    getChildren(element?: ChatTreeItem): Thenable<ChatTreeItem[]> {
        if (!element) {
            return Promise.resolve([
                new ChatTreeItem('New Chat', vscode.TreeItemCollapsibleState.None, 'openChat'),
                new ChatTreeItem('Clear History', vscode.TreeItemCollapsibleState.None, 'clearHistory')
            ]);
        }
        return Promise.resolve([]);
    }

    getTreeDataProvider(): vscode.TreeDataProvider<ChatTreeItem> {
        return this;
    }

    async openChat(): Promise<void> {
        if (this.chatPanel) {
            this.chatPanel.reveal(vscode.ViewColumn.Beside);
            return;
        }

        this.chatPanel = vscode.window.createWebviewPanel(
            'ollama-chat',
            'Ollama Assistant',
            vscode.ViewColumn.Beside,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [
                    vscode.Uri.joinPath(this.context.extensionUri, 'media'),
                    vscode.Uri.joinPath(this.context.extensionUri, 'out')
                ]
            }
        );

        this.chatPanel.webview.html = this.getWebviewContent();

        // Load models automatically when chat panel opens (with delay to ensure webview is ready)
        setTimeout(async () => {
            await this.handleGetModels();
        }, 100);

        // Handle messages from webview
        this.chatPanel.webview.onDidReceiveMessage(
            async (message) => {
                switch (message.type) {
                    case 'sendMessage':
                        await this.handleSendMessage(message.text, message.includeContext);
                        break;
                    case 'clearHistory':
                        await this.handleClearHistory();
                        break;
                    case 'getModels':
                        await this.handleGetModels();
                        break;
                    case 'selectModel':
                        await this.handleSelectModel(message.model);
                        break;
                    case 'applyCode':
                        await this.handleApplyCode(message.code, message.language);
                        break;
                    case 'createFile':
                        await this.handleCreateFile(message.filename, message.content, message.language);
                        break;
                    case 'fileOperation':
                        await this.handleFileOperation(message.operation, message.filename, message.content);
                        break;
                }
            },
            undefined,
            this.context.subscriptions
        );

        this.chatPanel.onDidDispose(() => {
            this.chatPanel = undefined;
        });

        // Send initial data
        await this.sendInitialData();
    }

    private async sendInitialData(): Promise<void> {
        if (!this.chatPanel) {
            return;
        }

        // Send conversation history
        this.chatPanel.webview.postMessage({
            type: 'conversationHistory',
            messages: this.conversationHistory
        });

        // Send available models
        try {
            const models = this.ollamaClient.getAvailableModels();
            this.chatPanel.webview.postMessage({
                type: 'availableModels',
                models: models.map(m => m.name)
            });
        } catch (error) {
            console.error('Error getting models:', error);
        }
    }

    private async handleSendMessage(text: string, includeContext: boolean): Promise<void> {
        if (!this.chatPanel || !text.trim()) {
            return;
        }

        const userMessage: ChatMessage = {
            role: 'user',
            content: text
        };

        // Add user message to history
        this.conversationHistory.push(userMessage);

        // Send user message to webview
        this.chatPanel.webview.postMessage({
            type: 'userMessage',
            message: userMessage
        });

        try {
            // Get context if requested
            let contextPrompt = '';
            if (includeContext) {
                const activeEditor = vscode.window.activeTextEditor;
                if (activeEditor) {
                    const context = await this.contextEngine.gatherContext(
                        activeEditor.document,
                        activeEditor.selection.active,
                        activeEditor.selection
                    );
                    contextPrompt = this.buildContextPrompt(context);
                }
            }

            // Prepare chat request
            const messages: ChatMessage[] = [...this.conversationHistory];
            
            // Add context to the last user message if available
            if (contextPrompt) {
                messages[messages.length - 1].content = `${contextPrompt}\n\nUser question: ${text}`;
            }

            const model = vscode.workspace.getConfiguration('ollama-assistant').get<string>('chatModel') || 
                         vscode.workspace.getConfiguration('ollama-assistant').get<string>('defaultModel') || '';

            if (!model) {
                this.chatPanel.webview.postMessage({
                    type: 'error',
                    message: 'No model selected. Please select a model first.'
                });
                return;
            }

            const request: ChatRequest = {
                model,
                messages,
                stream: true,
                options: vscode.workspace.getConfiguration('ollama-assistant').get('chatOptions') || {
                    temperature: 0.7,
                    top_p: 0.9,
                    num_ctx: 4096
                }
            };

            // Start streaming response
            let assistantMessage = '';
            this.chatPanel.webview.postMessage({
                type: 'assistantMessageStart'
            });

            for await (const chunk of this.ollamaClient.chatStream(request)) {
                if (chunk.message?.content) {
                    assistantMessage += chunk.message.content;
                    this.chatPanel.webview.postMessage({
                        type: 'assistantMessageChunk',
                        content: chunk.message.content
                    });
                }

                if (chunk.done) {
                    break;
                }
            }

            // Add assistant message to history
            const finalAssistantMessage: ChatMessage = {
                role: 'assistant',
                content: assistantMessage
            };
            this.conversationHistory.push(finalAssistantMessage);

            this.chatPanel.webview.postMessage({
                type: 'assistantMessageEnd',
                message: finalAssistantMessage
            });

        } catch (error) {
            console.error('Error sending message:', error);
            this.chatPanel.webview.postMessage({
                type: 'error',
                message: `Error: ${error}`
            });
        }
    }

    private buildContextPrompt(context: any): string {
        let prompt = 'CODEBASE_CONTEXT_START\n\n';

        // Current file information
        if (context.currentFile) {
            prompt += `CURRENT_FILE: ${context.currentFile.path}\n`;
            prompt += `LANGUAGE: ${context.currentFile.language}\n\n`;

            if (context.currentFile.selection) {
                prompt += 'SELECTED_CODE:\n```' + context.currentFile.language + '\n';
                prompt += context.currentFile.selection.text + '\n```\n\n';
            } else {
                // Include full file content for better context
                prompt += 'FULL_FILE_CONTENT:\n```' + context.currentFile.language + '\n';
                prompt += context.currentFile.content + '\n```\n\n';
            }
        }

        // Related files with content
        if (context.relatedFiles && context.relatedFiles.length > 0) {
            prompt += 'RELATED_FILES:\n';
            context.relatedFiles.slice(0, 3).forEach((file: any) => {
                prompt += `\nFILE: ${file.path} (${file.reason})\n`;
                prompt += '```' + (file.language || 'text') + '\n';
                // Include first 50 lines of related files
                const lines = file.content.split('\n').slice(0, 50);
                prompt += lines.join('\n');
                if (file.content.split('\n').length > 50) {
                    prompt += '\n... (truncated)';
                }
                prompt += '\n```\n';
            });
            prompt += '\n';
        }

        // Project structure
        if (context.projectContext) {
            prompt += 'PROJECT_CONTEXT:\n';

            if (context.projectContext.packageJson) {
                prompt += `Package: ${context.projectContext.packageJson.name}\n`;
                if (context.projectContext.packageJson.dependencies) {
                    prompt += 'Dependencies: ' + Object.keys(context.projectContext.packageJson.dependencies).slice(0, 10).join(', ') + '\n';
                }
            }

            if (context.projectContext.fileStructure) {
                prompt += 'File Structure:\n';
                context.projectContext.fileStructure.slice(0, 20).forEach((file: string) => {
                    prompt += `- ${file}\n`;
                });
                if (context.projectContext.fileStructure.length > 20) {
                    prompt += '... (and more files)\n';
                }
            }
            prompt += '\n';
        }

        prompt += 'CODEBASE_CONTEXT_END\n\n';
        prompt += 'INSTRUCTIONS:\n';
        prompt += '- When providing code suggestions, wrap them in proper markdown code blocks with language specification\n';
        prompt += '- For file operations, use the format: FILE_OPERATION:CREATE:filename.ext:content:END_FILE_OPERATION\n';
        prompt += '- For suggestions, use: SUGGESTION:your suggestion here:END_SUGGESTION\n';
        prompt += '- Be specific and actionable in your responses\n';
        prompt += '- Consider the full codebase context when making suggestions\n\n';

        return prompt;
    }

    private async handleClearHistory(): Promise<void> {
        this.conversationHistory = [];
        if (this.chatPanel) {
            this.chatPanel.webview.postMessage({
                type: 'historyCleared'
            });
        }
    }

    async clearHistory(): Promise<void> {
        await this.handleClearHistory();
    }

    private async handleApplyCode(code: string, language: string): Promise<void> {
        try {
            const activeEditor = vscode.window.activeTextEditor;
            if (!activeEditor) {
                vscode.window.showErrorMessage('No active editor to apply code to');
                return;
            }

            const selection = activeEditor.selection;
            await activeEditor.edit(editBuilder => {
                if (selection.isEmpty) {
                    // Insert at cursor position
                    editBuilder.insert(selection.active, code);
                } else {
                    // Replace selected text
                    editBuilder.replace(selection, code);
                }
            });

            vscode.window.showInformationMessage('Code applied successfully!');
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to apply code: ${error}`);
        }
    }

    private async handleCreateFile(filename: string, content: string, language: string): Promise<void> {
        try {
            const workspaceFolders = vscode.workspace.workspaceFolders;
            if (!workspaceFolders) {
                vscode.window.showErrorMessage('No workspace folder open');
                return;
            }

            const filePath = vscode.Uri.joinPath(workspaceFolders[0].uri, filename);

            // Check if file already exists
            try {
                await vscode.workspace.fs.stat(filePath);
                const overwrite = await vscode.window.showWarningMessage(
                    `File ${filename} already exists. Overwrite?`,
                    'Yes', 'No'
                );
                if (overwrite !== 'Yes') {
                    return;
                }
            } catch {
                // File doesn't exist, which is fine
            }

            // Create the file
            await vscode.workspace.fs.writeFile(filePath, Buffer.from(content, 'utf8'));

            // Open the file
            const document = await vscode.workspace.openTextDocument(filePath);
            await vscode.window.showTextDocument(document);

            vscode.window.showInformationMessage(`File ${filename} created successfully!`);
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to create file: ${error}`);
        }
    }

    private async handleFileOperation(operation: string, filename: string, content: string): Promise<void> {
        try {
            const workspaceFolders = vscode.workspace.workspaceFolders;
            if (!workspaceFolders) {
                vscode.window.showErrorMessage('No workspace folder open');
                return;
            }

            const filePath = vscode.Uri.joinPath(workspaceFolders[0].uri, filename);

            switch (operation.toLowerCase()) {
                case 'create':
                    await this.handleCreateFile(filename, content, '');
                    break;

                case 'edit':
                    try {
                        await vscode.workspace.fs.stat(filePath);
                        await vscode.workspace.fs.writeFile(filePath, Buffer.from(content, 'utf8'));

                        // Open the file to show changes
                        const document = await vscode.workspace.openTextDocument(filePath);
                        await vscode.window.showTextDocument(document);

                        vscode.window.showInformationMessage(`File ${filename} updated successfully!`);
                    } catch {
                        vscode.window.showErrorMessage(`File ${filename} not found`);
                    }
                    break;

                case 'delete':
                    try {
                        await vscode.workspace.fs.stat(filePath);
                        const confirm = await vscode.window.showWarningMessage(
                            `Are you sure you want to delete ${filename}?`,
                            'Yes', 'No'
                        );
                        if (confirm === 'Yes') {
                            await vscode.workspace.fs.delete(filePath);
                            vscode.window.showInformationMessage(`File ${filename} deleted successfully!`);
                        }
                    } catch {
                        vscode.window.showErrorMessage(`File ${filename} not found`);
                    }
                    break;

                default:
                    vscode.window.showErrorMessage(`Unknown operation: ${operation}`);
            }
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to execute ${operation}: ${error}`);
        }
    }

    private async handleGetModels(): Promise<void> {
        try {
            console.log('ChatProvider: Getting models...');
            await this.ollamaClient.refreshModels();
            const models = this.ollamaClient.getAvailableModels();
            console.log('ChatProvider: Found models:', models.map(m => m.name));
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'availableModels',
                    models: models.map(m => m.name)
                });
                console.log('ChatProvider: Sent models to webview');
            }
        } catch (error) {
            console.error('Error getting models:', error);
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'error',
                    message: 'Failed to load models'
                });
            }
        }
    }

    private async handleSelectModel(model: string): Promise<void> {
        try {
            await vscode.workspace.getConfiguration('ollama-assistant').update('chatModel', model, vscode.ConfigurationTarget.Global);
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'modelSelected',
                    model
                });
            }
        } catch (error) {
            console.error('Error selecting model:', error);
        }
    }

    private getWebviewContent(): string {
        const htmlContent = this.getHtmlTemplate();
        const jsContent = this.getJavaScriptCode();

        return htmlContent.replace('{{JAVASCRIPT_CODE}}', jsContent);
    }

    private getHtmlTemplate(): string {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ollama Assistant</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 20px;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--vscode-panel-border);
        }

        .header-title {
            font-size: 1.2em;
            font-weight: bold;
        }

        .model-selector {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 10px;
        }

        .model-controls {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        select {
            background-color: var(--vscode-dropdown-background);
            color: var(--vscode-dropdown-foreground);
            border: 1px solid var(--vscode-dropdown-border);
            padding: 8px;
            border-radius: 3px;
            min-width: 200px;
            flex: 1;
            max-width: 300px;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
            border: 1px solid var(--vscode-panel-border);
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 5px;
        }

        .user-message {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            margin-left: 20%;
        }

        .assistant-message {
            background-color: var(--vscode-editor-selectionBackground);
            margin-right: 20%;
        }

        .code-block {
            background-color: var(--vscode-textCodeBlock-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 6px;
            margin: 10px 0;
            overflow: hidden;
        }

        .code-header {
            background-color: var(--vscode-titleBar-inactiveBackground);
            padding: 8px 12px;
            border-bottom: 1px solid var(--vscode-panel-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
        }

        .code-actions {
            display: flex;
            gap: 8px;
        }

        .code-action-btn {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
            border: none;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
            font-family: inherit;
        }

        .code-action-btn:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);
        }

        .code-content {
            padding: 12px;
            font-family: var(--vscode-editor-font-family);
            font-size: var(--vscode-editor-font-size);
            line-height: 1.4;
            overflow-x: auto;
            white-space: pre;
        }

        .suggestion-block {
            background-color: var(--vscode-notifications-background);
            border: 1px solid var(--vscode-notifications-border);
            border-radius: 6px;
            margin: 10px 0;
            overflow: hidden;
        }

        .suggestion-header {
            background-color: var(--vscode-badge-background);
            color: var(--vscode-badge-foreground);
            padding: 8px 12px;
            font-weight: 500;
            font-size: 13px;
        }

        .suggestion-content {
            padding: 12px;
        }

        .suggestion-actions {
            padding: 8px 12px;
            border-top: 1px solid var(--vscode-panel-border);
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }

        .file-operation {
            background-color: var(--vscode-inputValidation-infoBackground);
            border: 1px solid var(--vscode-inputValidation-infoBorder);
            border-radius: 6px;
            margin: 10px 0;
            padding: 12px;
        }

        .file-operation-header {
            font-weight: 500;
            margin-bottom: 8px;
            color: var(--vscode-inputValidation-infoForeground);
        }

        .input-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
            padding: 10px;
            border-top: 1px solid var(--vscode-panel-border);
            background-color: var(--vscode-editor-background);
        }

        .input-wrapper {
            display: flex;
            flex-direction: column;
            width: 100%;
        }

        .input-row {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        textarea {
            width: 100%;
            min-height: 80px;
            max-height: 150px;
            padding: 12px;
            border: 1px solid var(--vscode-input-border);
            border-radius: 5px;
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            font-family: inherit;
            font-size: inherit;
            resize: vertical;
            box-sizing: border-box;
            line-height: 1.4;
        }

        .input-options {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 5px;
        }

        button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-family: inherit;
            white-space: nowrap;
            font-size: 13px;
            min-height: 28px;
        }

        .send-button {
            align-self: flex-end;
            padding: 10px 20px;
            font-size: 14px;
            font-weight: 500;
            min-width: 80px;
        }

        button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }

        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .error {
            color: var(--vscode-errorForeground);
            background-color: var(--vscode-inputValidation-errorBackground);
            border: 1px solid var(--vscode-inputValidation-errorBorder);
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
        }

        .typing-indicator {
            font-style: italic;
            opacity: 0.7;
        }

        pre {
            background-color: var(--vscode-textCodeBlock-background);
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            white-space: pre-wrap;
        }

        code {
            background-color: var(--vscode-textCodeBlock-background);
            padding: 2px 4px;
            border-radius: 3px;
        }

        /* Responsive design */
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }

            .header {
                gap: 10px;
                margin-bottom: 15px;
                padding-bottom: 10px;
            }

            .model-selector {
                flex-direction: column;
                align-items: stretch;
                gap: 8px;
            }

            select {
                min-width: unset;
                max-width: unset;
            }

            .model-controls {
                justify-content: space-between;
            }

            .user-message {
                margin-left: 2%;
            }

            .assistant-message {
                margin-right: 2%;
            }

            .send-button {
                align-self: stretch;
                margin-top: 5px;
            }
        }

        @media (max-width: 400px) {
            .model-controls {
                flex-direction: column;
            }

            button {
                padding: 10px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-title">Ollama Assistant</div>
        <div class="model-selector">
            <label for="model-select">Model:</label>
            <select id="model-select">
                <option value="">Select a model...</option>
            </select>
            <div class="model-controls">
                <button onclick="refreshModels()">Refresh</button>
                <button onclick="clearHistory()">Clear</button>
            </div>
        </div>
    </div>

    <div class="chat-container">
        <div id="messages" class="messages"></div>

        <div class="input-container">
            <div class="input-wrapper">
                <div class="input-options">
                    <label>
                        <input type="checkbox" id="include-context" checked>
                        Include current file context
                    </label>
                </div>
                <div class="input-row">
                    <textarea id="message-input" placeholder="Ask me anything about your code..."></textarea>
                    <button onclick="sendMessage()" id="send-button" class="send-button">Send</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        {{JAVASCRIPT_CODE}}
    </script>
</body>
</html>`;
    }

    private getJavaScriptCode(): string {
        return `
        const vscode = acquireVsCodeApi();
        let currentAssistantMessage = null;

        // Handle messages from extension
        window.addEventListener('message', event => {
            const message = event.data;
            console.log('Webview received message:', message);

            switch (message.type) {
                case 'conversationHistory':
                    displayConversationHistory(message.messages);
                    break;
                case 'availableModels':
                    console.log('Received availableModels message with models:', message.models);
                    updateModelSelector(message.models);
                    break;
                case 'userMessage':
                    displayUserMessage(message.message);
                    break;
                case 'assistantMessageStart':
                    startAssistantMessage();
                    break;
                case 'assistantMessageChunk':
                    appendToAssistantMessage(message.content);
                    break;
                case 'assistantMessageEnd':
                    endAssistantMessage();
                    break;
                case 'error':
                    displayError(message.message);
                    break;
                case 'historyCleared':
                    clearMessages();
                    break;
                case 'modelSelected':
                    document.getElementById('model-select').value = message.model;
                    break;
            }
        });

        function sendMessage() {
            const input = document.getElementById('message-input');
            const includeContext = document.getElementById('include-context').checked;
            const text = input.value.trim();

            if (!text) return;

            vscode.postMessage({
                type: 'sendMessage',
                text: text,
                includeContext: includeContext
            });

            input.value = '';
            document.getElementById('send-button').disabled = true;
        }

        function clearHistory() {
            vscode.postMessage({ type: 'clearHistory' });
        }

        function refreshModels() {
            vscode.postMessage({ type: 'getModels' });
        }

        function displayConversationHistory(messages) {
            const messagesDiv = document.getElementById('messages');
            messagesDiv.innerHTML = '';

            messages.forEach(message => {
                if (message.role === 'user') {
                    displayUserMessage(message);
                } else if (message.role === 'assistant') {
                    displayAssistantMessage(message);
                }
            });
        }

        function displayUserMessage(message) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message user-message';
            messageDiv.innerHTML = formatMessage(message.content);
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function displayAssistantMessage(message) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message assistant-message';
            messageDiv.innerHTML = formatMessage(message.content);
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function startAssistantMessage() {
            const messagesDiv = document.getElementById('messages');
            currentAssistantMessage = document.createElement('div');
            currentAssistantMessage.className = 'message assistant-message';
            currentAssistantMessage.innerHTML = '<div class="typing-indicator">Thinking...</div>';
            messagesDiv.appendChild(currentAssistantMessage);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function appendToAssistantMessage(content) {
            if (currentAssistantMessage) {
                const currentContent = currentAssistantMessage.textContent || '';
                const newContent = currentContent.replace('Thinking...', '') + content;
                currentAssistantMessage.innerHTML = formatMessage(newContent);

                const messagesDiv = document.getElementById('messages');
                messagesDiv.scrollTop = messagesDiv.scrollHeight;
            }
        }

        function endAssistantMessage() {
            currentAssistantMessage = null;
            document.getElementById('send-button').disabled = false;
        }

        function displayError(message) {
            const messagesDiv = document.getElementById('messages');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            messagesDiv.appendChild(errorDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
            document.getElementById('send-button').disabled = false;
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        function updateModelSelector(models) {
            console.log('updateModelSelector called with models:', models);
            const select = document.getElementById('model-select');
            if (!select) {
                console.error('model-select element not found!');
                return;
            }

            const currentValue = select.value;
            console.log('Current select value:', currentValue);

            select.innerHTML = '<option value="">Select a model...</option>';
            models.forEach(model => {
                const option = document.createElement('option');
                option.value = model;
                option.textContent = model;
                select.appendChild(option);
                console.log('Added model option:', model);
            });

            if (models.includes(currentValue)) {
                select.value = currentValue;
            }

            console.log('Model selector updated. Total options:', select.options.length);
        }

        function formatMessage(content) {
            // Advanced message formatting with code blocks and suggestions
            let formatted = content;

            // Handle code blocks with language and actions
            const codeBlockRegex = /\`\`\`(\\w+)?\\n?([\\s\\S]*?)\`\`\`/g;
            formatted = formatted.replace(codeBlockRegex, function(match, language, code) {
                const lang = language || 'text';
                const codeId = 'code_' + Math.random().toString(36).substr(2, 9);

                return '<div class="code-block">' +
                    '<div class="code-header">' +
                        '<span>' + lang + '</span>' +
                        '<div class="code-actions">' +
                            '<button class="code-action-btn" onclick="copyCode(\'' + codeId + '\')">Copy</button>' +
                            '<button class="code-action-btn" onclick="applyCode(\'' + codeId + '\', \'' + lang + '\')">Apply</button>' +
                            '<button class="code-action-btn" onclick="createFile(\'' + codeId + '\', \'' + lang + '\')">Create File</button>' +
                        '</div>' +
                    '</div>' +
                    '<div class="code-content" id="' + codeId + '">' + escapeHtml(code.trim()) + '</div>' +
                '</div>';
            });

            // Handle inline code
            formatted = formatted.replace(/\`([^\`]+)\`/g, '<code>$1</code>');

            // Handle file operations
            const fileOpRegex = /FILE_OPERATION:(CREATE|EDIT|DELETE):([^:]+):?([\\s\\S]*?)END_FILE_OPERATION/g;
            formatted = formatted.replace(fileOpRegex, function(match, operation, filename, content) {
                const opId = 'op_' + Math.random().toString(36).substr(2, 9);
                return '<div class="file-operation">' +
                    '<div class="file-operation-header">' + operation + ': ' + filename + '</div>' +
                    '<div class="suggestion-actions">' +
                        '<button class="code-action-btn" onclick="executeFileOperation(\'' + opId + '\', \'' + operation + '\', \'' + filename + '\', ' + JSON.stringify(content) + ')">' + (operation === 'DELETE' ? 'Delete' : 'Apply') + '</button>' +
                        '<button class="code-action-btn" onclick="declineOperation(\'' + opId + '\')">Decline</button>' +
                    '</div>' +
                '</div>';
            });

            // Handle suggestions
            const suggestionRegex = /SUGGESTION:([\\s\\S]*?)END_SUGGESTION/g;
            formatted = formatted.replace(suggestionRegex, function(match, suggestion) {
                const suggId = 'sugg_' + Math.random().toString(36).substr(2, 9);
                return '<div class="suggestion-block">' +
                    '<div class="suggestion-header">💡 Suggestion</div>' +
                    '<div class="suggestion-content">' + suggestion + '</div>' +
                    '<div class="suggestion-actions">' +
                        '<button class="code-action-btn" onclick="applySuggestion(\'' + suggId + '\')">Apply</button>' +
                        '<button class="code-action-btn" onclick="declineSuggestion(\'' + suggId + '\')">Decline</button>' +
                    '</div>' +
                '</div>';
            });

            // Convert newlines to breaks
            formatted = formatted.replace(/\\n/g, '<br>');

            return formatted;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Handle Enter key in textarea
        document.getElementById('message-input').addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Handle model selection
        document.getElementById('model-select').addEventListener('change', function(e) {
            if (e.target.value) {
                vscode.postMessage({
                    type: 'selectModel',
                    model: e.target.value
                });
            }
        });

        // Request initial data
        vscode.postMessage({ type: 'getModels' });

        // Code action handlers
        window.copyCode = function(codeId) {
            const codeElement = document.getElementById(codeId);
            if (codeElement) {
                navigator.clipboard.writeText(codeElement.textContent);
                showToast('Code copied to clipboard!');
            }
        };

        window.applyCode = function(codeId, language) {
            const codeElement = document.getElementById(codeId);
            if (codeElement) {
                vscode.postMessage({
                    type: 'applyCode',
                    code: codeElement.textContent,
                    language: language
                });
            }
        };

        window.createFile = function(codeId, language) {
            const codeElement = document.getElementById(codeId);
            if (codeElement) {
                const filename = prompt('Enter filename:', getDefaultFilename(language));
                if (filename) {
                    vscode.postMessage({
                        type: 'createFile',
                        filename: filename,
                        content: codeElement.textContent,
                        language: language
                    });
                }
            }
        };

        window.executeFileOperation = function(opId, operation, filename, content) {
            vscode.postMessage({
                type: 'fileOperation',
                operation: operation,
                filename: filename,
                content: content
            });
            showToast(\`\${operation} operation sent for \${filename}\`);
        };

        window.applySuggestion = function(suggId) {
            // Implementation for applying suggestions
            showToast('Suggestion applied!');
        };

        window.declineOperation = function(opId) {
            showToast('Operation declined');
        };

        window.declineSuggestion = function(suggId) {
            showToast('Suggestion declined');
        };

        function getDefaultFilename(language) {
            const extensions = {
                'javascript': '.js',
                'typescript': '.ts',
                'python': '.py',
                'java': '.java',
                'cpp': '.cpp',
                'c': '.c',
                'html': '.html',
                'css': '.css',
                'json': '.json',
                'xml': '.xml',
                'yaml': '.yml',
                'markdown': '.md'
            };
            return 'newfile' + (extensions[language] || '.txt');
        }

        function showToast(message) {
            // Simple toast notification
            const toast = document.createElement('div');
            toast.style.cssText =
                'position: fixed;' +
                'top: 20px;' +
                'right: 20px;' +
                'background: var(--vscode-notifications-background);' +
                'color: var(--vscode-notifications-foreground);' +
                'padding: 12px 16px;' +
                'border-radius: 4px;' +
                'border: 1px solid var(--vscode-notifications-border);' +
                'z-index: 1000;' +
                'font-size: 13px;';
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(function() {
                if (document.body.contains(toast)) {
                    document.body.removeChild(toast);
                }
            }, 3000);
        }

        // Initialize the webview by loading models
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, requesting models...');
            refreshModels();
        });

        // Also try to load models immediately in case DOM is already loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                console.log('DOM loaded (event), requesting models...');
                refreshModels();
            });
        } else {
            console.log('DOM already loaded, requesting models immediately...');
            refreshModels();
        }
        `;
    }

    dispose(): void {
        if (this.chatPanel) {
            this.chatPanel.dispose();
        }
    }
}

class ChatTreeItem extends vscode.TreeItem {
    constructor(
        public readonly label: string,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState,
        public readonly commandId?: string
    ) {
        super(label, collapsibleState);

        if (commandId) {
            this.command = {
                command: `ollama-assistant.${commandId}`,
                title: label
            };
        }
    }
}
