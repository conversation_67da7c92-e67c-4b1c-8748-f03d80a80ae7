# Development Guide

This guide will help you set up the development environment and understand the codebase structure for the Ollama Code Assistant VS Code extension.

## Prerequisites

- **Node.js**: Version 16 or higher
- **VS Code**: Version 1.80.0 or higher
- **Ollama**: Local installation with at least one model
- **Git**: For version control

## Setup Development Environment

### 1. <PERSON>lone and Install Dependencies

```bash
git clone <repository-url>
cd ollama-code-assistant
npm install
```

### 2. Install Ollama and Models

```bash
# Install Ollama (visit https://ollama.ai for platform-specific instructions)
curl -fsSL https://ollama.ai/install.sh | sh

# Start Ollama server
ollama serve

# Install recommended models
ollama pull codellama:7b
ollama pull mistral:7b
ollama pull llama2:7b
```

### 3. Build and Test

```bash
# Compile TypeScript
npm run compile

# Run linting
npm run lint

# Run tests
npm test

# Watch mode for development
npm run watch
```

### 4. Launch Extension

1. Open the project in VS Code
2. Press `F5` to launch Extension Development Host
3. Test the extension in the new VS Code window

## Project Structure

```
src/
├── extension.ts              # Main extension entry point
├── types/
│   └── ollama.ts            # TypeScript interfaces for Ollama API
├── services/
│   ├── ollamaClient.ts      # Ollama API client
│   ├── configurationManager.ts # VS Code configuration handling
│   └── contextEngine.ts     # Code context analysis
├── providers/
│   ├── chatProvider.ts      # Chat interface and webview
│   └── completionProvider.ts # Code completion provider
└── test/
    ├── runTest.ts           # Test runner
    └── suite/
        ├── index.ts         # Test suite setup
        └── extension.test.ts # Extension tests
```

## Key Components

### 1. Extension Entry Point (`extension.ts`)

The main activation function that:
- Initializes all services
- Registers commands and providers
- Sets up VS Code integrations

### 2. Ollama Client (`services/ollamaClient.ts`)

Handles all communication with the Ollama server:
- HTTP client with retry logic
- Streaming response handling
- Model discovery and management
- Error handling and connection monitoring

### 3. Context Engine (`services/contextEngine.ts`)

Analyzes the codebase to provide relevant context:
- File relationship detection
- Import/dependency parsing
- Project structure analysis
- Smart context bundling

### 4. Completion Provider (`providers/completionProvider.ts`)

Integrates with VS Code's completion API:
- Real-time code completions
- Debounced requests
- Response caching
- Context-aware prompts

### 5. Chat Provider (`providers/chatProvider.ts`)

Manages the chat interface:
- Webview-based UI
- Streaming conversations
- Context inclusion
- Model selection

## Development Workflow

### 1. Making Changes

1. Make your changes to the TypeScript files
2. Run `npm run compile` or use watch mode (`npm run watch`)
3. Press `Ctrl+R` in the Extension Development Host to reload
4. Test your changes

### 2. Adding New Features

1. Create feature branch: `git checkout -b feature/new-feature`
2. Implement the feature following existing patterns
3. Add tests if applicable
4. Update documentation
5. Submit pull request

### 3. Testing

```bash
# Run all tests
npm test

# Run specific test file
npm test -- --grep "specific test name"

# Run tests in watch mode
npm run test:watch
```

## Configuration

### Extension Settings

The extension uses VS Code's configuration system. Settings are defined in `package.json` under `contributes.configuration`.

### Environment Variables

For development, you can create a `.env` file:

```env
OLLAMA_SERVER_URL=http://localhost:11434
DEFAULT_MODEL=codellama:7b
DEBUG=true
```

## Debugging

### 1. Extension Debugging

- Set breakpoints in TypeScript files
- Press `F5` to launch with debugger attached
- Use VS Code's debug console

### 2. Webview Debugging

- Right-click in the chat webview
- Select "Inspect Element" to open DevTools
- Debug the webview's HTML/CSS/JavaScript

### 3. Ollama API Debugging

- Check Ollama logs: `ollama logs`
- Monitor network requests in browser DevTools
- Use `curl` to test API endpoints directly

## Common Issues

### TypeScript Compilation Errors

```bash
# Clean and rebuild
rm -rf out/
npm run compile
```

### Extension Not Loading

1. Check the Output panel for errors
2. Verify all dependencies are installed
3. Ensure Ollama is running
4. Check VS Code version compatibility

### Ollama Connection Issues

1. Verify Ollama is running: `ollama list`
2. Check server URL in settings
3. Test API manually: `curl http://localhost:11434/api/tags`

## Code Style

### TypeScript Guidelines

- Use strict TypeScript settings
- Prefer interfaces over types for object shapes
- Use async/await over Promises
- Add JSDoc comments for public APIs

### Naming Conventions

- Classes: PascalCase (`OllamaClient`)
- Methods/Variables: camelCase (`getModels`)
- Constants: UPPER_SNAKE_CASE (`MAX_RETRIES`)
- Files: camelCase (`ollamaClient.ts`)

### Error Handling

- Always handle async operations with try/catch
- Provide user-friendly error messages
- Log detailed errors for debugging
- Implement graceful degradation

## Performance Considerations

### 1. Completion Performance

- Use debouncing to reduce API calls
- Implement caching for repeated requests
- Limit context size to manage token usage
- Consider model size vs. speed tradeoffs

### 2. Memory Management

- Dispose of resources properly
- Clear caches periodically
- Avoid memory leaks in webviews
- Monitor extension memory usage

### 3. Network Optimization

- Use streaming for long responses
- Implement request queuing
- Add timeout handling
- Retry failed requests with backoff

## Contributing

### Pull Request Process

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Update documentation
7. Submit pull request

### Code Review Checklist

- [ ] Code follows style guidelines
- [ ] Tests are included and passing
- [ ] Documentation is updated
- [ ] No breaking changes (or properly documented)
- [ ] Performance impact considered
- [ ] Error handling implemented

## Release Process

### 1. Version Bump

```bash
npm version patch|minor|major
```

### 2. Package Extension

```bash
npm run package
```

### 3. Test Package

```bash
code --install-extension ollama-code-assistant-*.vsix
```

### 4. Publish

```bash
npm run publish
```

## Resources

- [VS Code Extension API](https://code.visualstudio.com/api)
- [Ollama API Documentation](https://github.com/ollama/ollama/blob/main/docs/api.md)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Mocha Testing Framework](https://mochajs.org/)

## Support

- **Issues**: Report bugs on GitHub Issues
- **Discussions**: Use GitHub Discussions for questions
- **Documentation**: Check the README and this guide
- **Community**: Join the project Discord/Slack (if available)
