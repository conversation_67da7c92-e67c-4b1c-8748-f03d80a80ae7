import * as vscode from 'vscode';
import { OllamaClient } from './services/ollamaClient';
import { ModernChatProvider } from './providers/modernChatProvider';
import { TaskChatProvider } from './providers/taskChatProvider';
import { CompletionProvider } from './providers/completionProvider';
import { ContextEngine } from './services/contextEngine';
import { ConfigurationManager } from './services/configurationManager';

let ollamaClient: OllamaClient;
let chatProvider: ChatProvider;
let taskChatProvider: TaskChatProvider;
let completionProvider: CompletionProvider;
let contextEngine: ContextEngine;
let configManager: ConfigurationManager;
let statusBarItem: vscode.StatusBarItem;

export async function activate(context: vscode.ExtensionContext) {
    console.log('Ollama Code Assistant is now active!');

    // Initialize services
    configManager = new ConfigurationManager();
    ollamaClient = new OllamaClient(configManager);
    contextEngine = new ContextEngine();
    chatProvider = new ChatProvider(context, ollamaClient, contextEngine);
    taskChatProvider = new TaskChatProvider(context, ollamaClient);
    completionProvider = new CompletionProvider(ollamaClient, contextEngine, configManager);

    // Register commands
    const openChatCommand = vscode.commands.registerCommand('ollama-assistant.openChat', () => {
        console.log('openChat command triggered');
        chatProvider.openChat();
    });

    const openTaskChatCommand = vscode.commands.registerCommand('ollama-assistant.openTaskChat', () => {
        console.log('openTaskChat command triggered');
        taskChatProvider.openTaskChat();
    });

    const selectModelCommand = vscode.commands.registerCommand('ollama-assistant.selectModel', async () => {
        await selectModel();
    });

    const refreshModelsCommand = vscode.commands.registerCommand('ollama-assistant.refreshModels', async () => {
        console.log('refreshModels command triggered');
        await ollamaClient.refreshModels();
        vscode.window.showInformationMessage('Models refreshed successfully!');
    });

    const clearHistoryCommand = vscode.commands.registerCommand('ollama-assistant.clearHistory', async () => {
        console.log('clearHistory command triggered');
        await chatProvider.clearHistory();
        vscode.window.showInformationMessage('Chat history cleared!');
    });

    const clearTaskHistoryCommand = vscode.commands.registerCommand('ollama-assistant.clearTaskHistory', async () => {
        console.log('clearTaskHistory command triggered');
        // The method will be called via webview message, so just show info
        vscode.window.showInformationMessage('Task history cleared!');
    });

    // Register providers
    const completionDisposable = vscode.languages.registerInlineCompletionItemProvider(
        { pattern: '**' },
        completionProvider
    );

    // Register tree views
    const treeDataProvider = chatProvider.getTreeDataProvider();
    vscode.window.createTreeView('ollama-assistant.chatView', {
        treeDataProvider,
        showCollapseAll: true
    });

    const taskTreeDataProvider = taskChatProvider.getTreeDataProvider();
    vscode.window.createTreeView('ollama-assistant.taskChatView', {
        treeDataProvider: taskTreeDataProvider,
        showCollapseAll: true
    });

    // Create status bar item for model selection
    statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
    statusBarItem.command = 'ollama-assistant.selectModel';
    statusBarItem.tooltip = 'Click to select Ollama model';
    updateStatusBarItem();
    statusBarItem.show();

    // Add to subscriptions
    context.subscriptions.push(
        openChatCommand,
        openTaskChatCommand,
        selectModelCommand,
        refreshModelsCommand,
        clearHistoryCommand,
        clearTaskHistoryCommand,
        completionDisposable,
        statusBarItem
    );

    // Initialize connection to Ollama
    try {
        await ollamaClient.initialize();
        const models = ollamaClient.getAvailableModels();
        vscode.window.showInformationMessage(`Connected to Ollama server successfully! Found ${models.length} models.`);
    } catch (error) {
        console.error('Failed to initialize Ollama client:', error);
        vscode.window.showErrorMessage(`Failed to connect to Ollama: ${error}`);
    }
}

async function selectModel() {
    try {
        // Refresh models first to ensure we have the latest list
        await ollamaClient.refreshModels();
        const models = ollamaClient.getAvailableModels();

        if (models.length === 0) {
            vscode.window.showWarningMessage('No models available. Please ensure Ollama is running and models are installed.');
            return;
        }

        const modelNames = models.map(model => model.name);
        const selected = await vscode.window.showQuickPick(modelNames, {
            placeHolder: 'Select an Ollama model'
        });

        if (selected) {
            await configManager.setDefaultModel(selected);
            await configManager.setChatModel(selected);
            updateStatusBarItem();
            vscode.window.showInformationMessage(`🤖 Selected model: ${selected}`);
        }
    } catch (error) {
        console.error('Error in selectModel:', error);
        vscode.window.showErrorMessage(`Failed to load models: ${error}`);
    }
}

function updateStatusBarItem() {
    const currentModel = configManager.getChatModel() || configManager.getDefaultModel();
    if (currentModel) {
        statusBarItem.text = `🤖 ${currentModel}`;
        statusBarItem.backgroundColor = undefined;
    } else {
        statusBarItem.text = `🤖 No Model`;
        statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.warningBackground');
    }
}

export function deactivate() {
    // Cleanup resources
    if (ollamaClient) {
        ollamaClient.dispose();
    }
    if (chatProvider) {
        chatProvider.dispose();
    }
    if (taskChatProvider) {
        taskChatProvider.dispose();
    }
}
