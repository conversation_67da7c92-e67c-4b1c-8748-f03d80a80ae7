import * as vscode from 'vscode';
import { OllamaClient } from '../services/ollamaClient';
import { AdvancedContextEngine } from './contextEngine';
import { MemorySystem } from './memorySystem';
import { CodeCheckpoints } from './codeCheckpoints';
import { ToolRegistry } from './toolRegistry';

export interface AgentCapabilities {
    chat: boolean;
    nextEdit: boolean;
    completions: boolean;
    codeGeneration: boolean;
    fileOperations: boolean;
    terminalCommands: boolean;
    multiModal: boolean;
}

export interface AgentMemory {
    id: string;
    timestamp: Date;
    context: string;
    codeStyle: any;
    patterns: string[];
    preferences: any;
}

export interface StreamCommand {
    action: 'create_file' | 'modify_file' | 'run_command' | 'task_complete' | 'chat' | 'delete_file' | 'apply_code';
    file_path?: string;
    content?: string;
    command?: string;
    task_description: string;
    is_complete: boolean;
    code?: string;
    language?: string;
}

export interface CommandExtractionResult {
    complete: StreamCommand[];
    remaining: string;
}

export interface ChatResponse {
    content: string;
    suggestAgentMode: boolean;
    agentModeReason?: string;
    type: 'chat';
}

export interface AgentResponse {
    content: string;
    actions: AgentAction[];
    nextEdits?: NextEdit[];
    memories?: AgentMemory[];
    sources?: string[];
}

export interface TaskResponse {
    action: 'create_file' | 'modify_file' | 'delete_file' | 'run_command' | 'chat' | 'completed';
    file_path?: string;
    content?: string;
    command?: string;
    message?: string;
    task_description: string;
    next_task: string | null;
}

export interface AgentAction {
    type: 'create_file' | 'modify_file' | 'delete_file' | 'run_command' | 'apply_code' | 'next_edit';
    target: string;
    content?: string;
    description: string;
    confidence: number;
}

export interface NextEdit {
    id: string;
    file: string;
    line: number;
    column: number;
    type: 'insert' | 'replace' | 'delete';
    content: string;
    description: string;
    dependencies: string[];
}

export class TaskAgent {
    private contextEngine: AdvancedContextEngine;
    private currentTask: string | null = null;
    private taskHistory: Array<{task: string, response: TaskResponse}> = [];

    constructor(
        private ollamaClient: OllamaClient,
        private context: vscode.ExtensionContext
    ) {
        this.contextEngine = new AdvancedContextEngine();
    }

    async processTask(
        userRequest: string,
        includeContext: boolean = true,
        activeFile?: vscode.TextDocument,
        selection?: vscode.Selection
    ): Promise<TaskResponse> {
        try {
            // Gather comprehensive context
            const contextBundle = includeContext ?
                await this.contextEngine.gatherComprehensiveContext(activeFile, selection) :
                null;

            // Build the task-oriented prompt
            const prompt = this.buildTaskPrompt(userRequest, contextBundle);

            // Get AI response
            const model = this.getSelectedModel();
            let fullResponse = '';

            for await (const chunk of this.ollamaClient.chatStream({
                model,
                messages: [{ role: 'user', content: prompt }],
                options: this.getOptimizedOptions()
            })) {
                if (chunk.message?.content) {
                    fullResponse += chunk.message.content;
                }
            }

            // Parse the JSON response
            const taskResponse = this.parseTaskResponse(fullResponse);

            // Store current task for follow-up
            this.currentTask = taskResponse.next_task;
            this.taskHistory.push({ task: userRequest, response: taskResponse });

            return taskResponse;

        } catch (error) {
            console.error('Task processing error:', error);
            return {
                action: 'chat',
                message: `Error processing task: ${error}`,
                task_description: 'Error occurred',
                next_task: null
            };
        }
    }

    async processNextTask(): Promise<TaskResponse | null> {
        if (!this.currentTask) {
            return null;
        }

        const followUpPrompt = this.buildFollowUpPrompt(this.currentTask);

        try {
            const model = this.getSelectedModel();
            let fullResponse = '';

            for await (const chunk of this.ollamaClient.chatStream({
                model,
                messages: [{ role: 'user', content: followUpPrompt }],
                options: this.getOptimizedOptions()
            })) {
                if (chunk.message?.content) {
                    fullResponse += chunk.message.content;
                }
            }

            const taskResponse = this.parseTaskResponse(fullResponse);

            // Update current task
            this.currentTask = taskResponse.next_task;
            this.taskHistory.push({ task: this.currentTask || 'follow-up', response: taskResponse });

            return taskResponse;

        } catch (error) {
            console.error('Next task processing error:', error);
            return {
                action: 'chat',
                message: `Error processing next task: ${error}`,
                task_description: 'Error occurred',
                next_task: null
            };
        }
    }

    private buildTaskPrompt(userRequest: string, contextBundle: any): string {
        let prompt = `You are a highly capable AI agent for a VS Code extension. Your goal is to complete complex programming tasks by breaking them down into small, single, atomic commands. For every task, you will provide a single JSON response that contains the **one action to be executed now** and a brief description of the **next logical task to complete the overall goal.**

**Task-Oriented Response Format:**

Your response **must be a single JSON object only**. It should have the following structure:

{
  "action": "The command for me to execute now (e.g., create_file, modify_file, run_command).",
  "file_path": "Relevant file path (for file actions).",
  "content": "Full content of the file (for file actions).",
  "command": "The shell command to run (for run_command).",
  "task_description": "A brief, human-readable summary of what you just did.",
  "next_task": "A brief, human-readable description of the next task required to complete the user's request, or null if the entire task is complete."
}

**CRITICAL RULES:**
1. Your response must contain ONLY a valid JSON object - no preambles, explanations, or follow-up text
2. Start your response with { and end with }
3. For file operations, provide the COMPLETE file content, not just snippets
4. For new projects requiring dependencies, your first action must be run_command with npm install
5. Break complex tasks into small, atomic steps
6. Only provide the FIRST step - wait for confirmation before proceeding

`;

        if (contextBundle) {
            prompt += `\n**CURRENT WORKSPACE CONTEXT:**\n`;
            if (contextBundle.workspace) {
                prompt += `- Root: ${contextBundle.workspace.rootPath}\n`;
                prompt += `- Files: ${contextBundle.workspace.fileCount} files\n`;
                prompt += `- Languages: ${contextBundle.workspace.languages.join(', ')}\n`;
            }

            if (contextBundle.activeFile) {
                prompt += `\n**ACTIVE FILE:**\n`;
                prompt += `- Path: ${contextBundle.activeFile.path}\n`;
                prompt += `- Language: ${contextBundle.activeFile.language}\n`;

                if (contextBundle.activeFile.content) {
                    prompt += `- Content:\n\`\`\`${contextBundle.activeFile.language}\n${contextBundle.activeFile.content}\n\`\`\`\n`;
                }
            }

            if (contextBundle.dependencies?.length > 0) {
                prompt += `\n**EXISTING DEPENDENCIES:**\n`;
                contextBundle.dependencies.forEach((dep: any) => {
                    prompt += `- ${dep.name}@${dep.version}\n`;
                });
            }
        }

        prompt += `\n**USER REQUEST:** ${userRequest}\n\n`;
        prompt += `Respond with a single JSON object for the FIRST action to take:`;

        return prompt;
    }

    private buildFollowUpPrompt(nextTask: string): string {
        const lastTask = this.taskHistory[this.taskHistory.length - 1];

        return `The previous task "${lastTask.task}" is complete. The task description was: "${lastTask.response.task_description}".

The next task is: "${nextTask}"

Please provide the JSON for this next step. Remember:
- Your response must be a single JSON object only
- No preambles, explanations, or follow-up text
- Start with { and end with }
- Provide complete file content for file operations

Respond with the JSON for the next action:`;
    }

    private parseTaskResponse(response: string): TaskResponse {
        try {
            // Clean the response - remove any non-JSON content
            const cleanedResponse = response.trim();

            // Try to find JSON in the response
            const jsonMatch = cleanedResponse.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                throw new Error('No JSON found in response');
            }

            const jsonResponse = JSON.parse(jsonMatch[0]);

            // Validate required fields
            if (!jsonResponse.action || !jsonResponse.task_description) {
                throw new Error('Invalid JSON structure - missing required fields');
            }

            return {
                action: jsonResponse.action,
                file_path: jsonResponse.file_path,
                content: jsonResponse.content,
                command: jsonResponse.command,
                message: jsonResponse.message,
                task_description: jsonResponse.task_description,
                next_task: jsonResponse.next_task
            };

        } catch (error) {
            console.error('Failed to parse task response:', error);
            console.error('Raw response:', response);

            // Return a fallback response
            return {
                action: 'chat',
                message: `I encountered an error parsing the response. Raw response: ${response}`,
                task_description: 'Error parsing response',
                next_task: null
            };
        }
    }

    private getSelectedModel(): string {
        return vscode.workspace.getConfiguration('ollama-assistant').get('chatModel') || 'llama2';
    }

    private getOptimizedOptions(): any {
        return {
            temperature: 0.1,
            top_p: 0.9,
            num_ctx: 8192
        };
    }

    hasNextTask(): boolean {
        return this.currentTask !== null;
    }

    getCurrentTask(): string | null {
        return this.currentTask;
    }

    getTaskHistory(): Array<{task: string, response: TaskResponse}> {
        return [...this.taskHistory];
    }

    clearTaskHistory(): void {
        this.currentTask = null;
        this.taskHistory = [];
    }
}

export class AugmentAgent {
    private capabilities: AgentCapabilities;
    private memorySystem: MemorySystem;
    private contextEngine: AdvancedContextEngine;
    private checkpoints: CodeCheckpoints;
    private toolRegistry: ToolRegistry;
    private conversationHistory: any[] = [];

    constructor(
        private ollamaClient: OllamaClient,
        private context: vscode.ExtensionContext
    ) {
        this.capabilities = {
            chat: true,
            nextEdit: true,
            completions: true,
            codeGeneration: true,
            fileOperations: true,
            terminalCommands: true,
            multiModal: false // Will be implemented later
        };

        this.memorySystem = new MemorySystem(context);
        this.contextEngine = new AdvancedContextEngine();
        this.checkpoints = new CodeCheckpoints(context);
        this.toolRegistry = new ToolRegistry();

        this.initializeTools();
    }

    private initializeTools(): void {
        // Register built-in tools
        this.toolRegistry.register('create_file', this.createFile.bind(this));
        this.toolRegistry.register('modify_file', this.modifyFile.bind(this));
        this.toolRegistry.register('delete_file', this.deleteFile.bind(this));
        this.toolRegistry.register('run_command', this.runCommand.bind(this));
        this.toolRegistry.register('apply_code', this.applyCode.bind(this));
        this.toolRegistry.register('get_context', this.getContext.bind(this));
    }

    async processRequest(
        message: string,
        includeContext: boolean = true,
        activeFile?: vscode.TextDocument,
        selection?: vscode.Selection
    ): Promise<AgentResponse> {
        try {
            // Create checkpoint before making changes
            const checkpointId = await this.checkpoints.create('Before agent action');

            // Gather comprehensive context
            const contextBundle = includeContext ?
                await this.contextEngine.gatherComprehensiveContext(activeFile, selection) :
                null;

            // Retrieve relevant memories
            const relevantMemories = await this.memorySystem.getRelevantMemories(message, contextBundle);

            // Build enhanced system prompt
            const systemPrompt = this.buildEnhancedSystemPrompt(contextBundle, relevantMemories);

            // Build structured context for the user message
            const contextualMessage = this.buildContextualMessage(message, contextBundle);

            // Prepare conversation with context
            const messages = [
                { role: 'system', content: systemPrompt },
                ...this.conversationHistory,
                { role: 'user', content: contextualMessage }
            ];

            // Get AI response
            let fullResponse = '';
            for await (const chunk of this.ollamaClient.chatStream({
                model: this.getSelectedModel(),
                messages,
                options: this.getOptimizedOptions()
            })) {
                if (chunk.message?.content) {
                    fullResponse += chunk.message.content;
                }
            }

            // Parse response and extract actions
            const parsedResponse = this.parseAgentResponse(fullResponse);

            // Execute actions if any
            if (parsedResponse.actions.length > 0) {
                await this.executeActions(parsedResponse.actions);
            }

            // Update memories
            await this.memorySystem.updateMemories(message, parsedResponse, contextBundle);

            // Add to conversation history
            this.conversationHistory.push(
                { role: 'user', content: message },
                { role: 'assistant', content: fullResponse }
            );

            return parsedResponse;

        } catch (error) {
            console.error('Agent processing error:', error);
            throw error;
        }
    }

    /**
     * NEW: Streaming Command Pipeline - processes a request and streams multiple commands
     */
    async processRequestStream(
        message: string,
        includeContext: boolean = true,
        activeFile?: vscode.TextDocument,
        selection?: vscode.Selection,
        onCommand?: (command: StreamCommand) => Promise<void>,
        mode: 'agent' | 'agent-auto' = 'agent'
    ): Promise<void> {
        try {
            // Create checkpoint before making changes
            const checkpointId = await this.checkpoints.create('Before streaming agent action');

            // Gather comprehensive context
            const contextBundle = includeContext ?
                await this.contextEngine.gatherComprehensiveContext(activeFile, selection) :
                null;

            // Retrieve relevant memories
            const relevantMemories = await this.memorySystem.getRelevantMemories(message, contextBundle);

            // Build streaming system prompt
            const systemPrompt = this.buildStreamingSystemPrompt(contextBundle, relevantMemories, mode);

            // Build structured context for the user message
            const contextualMessage = this.buildContextualMessage(message, contextBundle);

            // Prepare conversation with context
            const messages = [
                { role: 'system', content: systemPrompt },
                ...this.conversationHistory,
                { role: 'user', content: contextualMessage }
            ];

            // Stream and process commands in real-time
            let buffer = '';
            let commandCount = 0;

            for await (const chunk of this.ollamaClient.chatStream({
                model: this.getSelectedModel(),
                messages,
                options: this.getOptimizedOptions()
            })) {
                if (chunk.message?.content) {
                    buffer += chunk.message.content;

                    // Try to extract complete JSON commands from buffer
                    const commands = this.extractCommandsFromBuffer(buffer);

                    for (const command of commands.complete) {
                        commandCount++;
                        console.log(`Executing command ${commandCount}:`, command);

                        // Execute command immediately
                        if (onCommand) {
                            await onCommand(command);
                        }

                        // Execute the actual action
                        await this.executeStreamCommand(command);

                        // If this is the completion command, break
                        if (command.is_complete) {
                            console.log('Streaming completed successfully');
                            return;
                        }
                    }

                    // Update buffer to remaining incomplete content
                    buffer = commands.remaining;
                }
            }

            // Process any remaining commands in buffer
            if (buffer.trim()) {
                const finalCommands = this.extractCommandsFromBuffer(buffer, true);
                for (const command of finalCommands.complete) {
                    if (onCommand) {
                        await onCommand(command);
                    }
                    await this.executeStreamCommand(command);
                }
            }

        } catch (error) {
            console.error('Streaming agent processing error:', error);
            throw error;
        }
    }

    private buildEnhancedSystemPrompt(contextBundle: any, memories: AgentMemory[]): string {
        let prompt = `[SYSTEM INSTRUCTIONS]
You are **Augment Agent**, an expert software engineering AI assistant. Your primary goal is to act as a highly proficient and proactive pair programmer. You are not a simple chatbot. You understand complex codebases, architecture, best practices, and project structure. You are capable of multi-step problem solving and breaking down large tasks into manageable, executable actions. You will operate in an **Agent** mode by default, unless explicitly asked to switch to **Chat** mode. Your responses for Agent mode will be in the form of a structured JSON command.

For every user request, you will follow this exact, multi-step process. **Do not skip any steps.**

**Step 1: ANALYSIS & UNDERSTANDING.**
* Analyze the user's request and the provided codebase context.
* Identify all necessary files, dependencies, and architectural changes required.
* Formulate a high-level plan to achieve the user's goal.

**Step 2: PLANNING & EXECUTION.**
* Break down the high-level plan into a series of small, atomic, and actionable agent commands.
* Identify the very first action to take. This could be creating a file, modifying a file, or installing a dependency.
* Generate a JSON object for **only the first command.** You will wait for me to confirm or execute this before proceeding to the next step.

**Step 3: DEPENDENCY & ENVIRONMENT SETUP.**
* Before writing any code that requires external libraries (e.g., React, Express, etc.), your first action must be to create a command to install the necessary dependencies (npm install). This is a non-negotiable step for any new project or feature.

**ACTION CHECKLIST:**
Before you output any code, ask yourself these questions and incorporate the answers into your final JSON response:
* Does this task require new files? If yes, identify the correct directory and file name.
* Does this task require new dependencies? If yes, your first action must be to generate a JSON to run npm install [package-name].
* Does this code require imports? If yes, ensure all necessary imports are at the top of the file.
* What is the full content of the file? When modifying a file, do not just provide the new function. Provide the *entire* file content, including existing code, so the extension can replace the old content completely.

**FINAL RULE: JSON OUTPUT ONLY.**
Your response **must contain nothing but a single JSON object**. Do not include any preambles, explanations, internal monologues, or follow-up text. Just the raw JSON.

**JSON FORMATS:**
File creation: {"action": "create_file", "file_path": "filename.js", "content": "complete file content"}
File modification: {"action": "modify_file", "file_path": "filename.js", "content": "complete file content"}
File deletion: {"action": "delete_file", "file_path": "filename.js"}
Command execution: {"action": "run_command", "command": "npm install package-name"}
Chat response: {"action": "chat", "message": "response here"}`;

        if (contextBundle) {
            prompt += `\n\nCURRENT WORKSPACE:`;
            if (contextBundle.workspace) {
                prompt += `\n- Root: ${contextBundle.workspace.rootPath}`;
                prompt += `\n- Files: ${contextBundle.workspace.fileCount} files`;
                prompt += `\n- Languages: ${contextBundle.workspace.languages.join(', ')}`;
            }

            if (contextBundle.activeFile) {
                prompt += `\n\nACTIVE FILE:`;
                prompt += `\n- Path: ${contextBundle.activeFile.path}`;
                prompt += `\n- Language: ${contextBundle.activeFile.language}`;
                prompt += `\n- Lines: ${contextBundle.activeFile.lineCount}`;
                
                if (contextBundle.activeFile.selection) {
                    prompt += `\n- Selected: ${contextBundle.activeFile.selection.text}`;
                }
            }

            if (contextBundle.relatedFiles?.length > 0) {
                prompt += `\n\nRELATED FILES:`;
                contextBundle.relatedFiles.forEach((file: any) => {
                    prompt += `\n- ${file.path} (${file.relationship})`;
                });
            }

            if (contextBundle.dependencies?.length > 0) {
                prompt += `\n\nDEPENDENCIES:`;
                contextBundle.dependencies.forEach((dep: any) => {
                    prompt += `\n- ${dep.name}@${dep.version}`;
                });
            }
        }

        if (memories.length > 0) {
            prompt += `\n\nRELEVANT MEMORIES:`;
            memories.forEach(memory => {
                prompt += `\n- ${memory.context}`;
                if (memory.patterns.length > 0) {
                    prompt += ` (Patterns: ${memory.patterns.join(', ')})`;
                }
            });
        }

        prompt += `\n\nRemember: Provide clear, actionable responses with proper code formatting and specific file operations.`;

        return prompt;
    }

    private buildContextualMessage(userMessage: string, contextBundle: any): string {
        let contextualMessage = '';

        if (contextBundle) {
            contextualMessage += '[CONTEXT]\n';

            // Active File Information
            if (contextBundle.activeFile) {
                contextualMessage += `Active File:\n${contextBundle.activeFile.path}\n`;

                if (contextBundle.activeFile.content) {
                    contextualMessage += `${contextBundle.activeFile.content}\n\n`;
                }

                if (contextBundle.activeFile.selection) {
                    contextualMessage += `Selected code:\n${contextBundle.activeFile.selection}\n\n`;
                }
            }

            // Project Structure
            if (contextBundle.workspace && contextBundle.workspace.fileStructure) {
                contextualMessage += 'Project Structure:\n';
                contextBundle.workspace.fileStructure.slice(0, 30).forEach((file: string) => {
                    contextualMessage += `- ${file}\n`;
                });
                if (contextBundle.workspace.fileStructure.length > 30) {
                    contextualMessage += `... and ${contextBundle.workspace.fileStructure.length - 30} more files\n`;
                }
            }

            contextualMessage += '[/CONTEXT]\n\n';
        }

        contextualMessage += '[INSTRUCTIONS]\n';
        contextualMessage += `The user wants to: ${userMessage}\n`;
        contextualMessage += 'CRITICAL: Your response must be ONLY a sequence of valid JSON objects, one per line.\n';
        contextualMessage += 'Break this request into logical steps and output one JSON command for each step.\n';
        contextualMessage += 'Do NOT include any explanatory text, preambles, or formatting.\n';
        contextualMessage += 'Each line must be a complete, valid JSON object.\n';
        contextualMessage += 'Only the final JSON should have "is_complete": true.\n';
        contextualMessage += '[/INSTRUCTIONS]';

        return contextualMessage;
    }

    private parseAgentResponse(response: string): AgentResponse {
        const actions: AgentAction[] = [];
        const nextEdits: NextEdit[] = [];
        const sources: string[] = [];
        let content = response;

        try {
            // First try to parse the entire response as JSON
            const jsonResponse = JSON.parse(response.trim());
            return this.processJsonResponse(jsonResponse);
        } catch (error) {
            console.log('Full response is not JSON, attempting to extract JSON...');

            // Try to extract JSON from mixed content
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                try {
                    const extractedJson = JSON.parse(jsonMatch[0]);
                    console.log('Successfully extracted JSON from mixed response');
                    return this.processJsonResponse(extractedJson);
                } catch (extractError) {
                    console.log('Extracted text is not valid JSON:', extractError);
                }
            }

            // If no valid JSON found, treat as chat response
            console.log('No valid JSON found, treating as chat response');
            content = response;
        }

        return {
            content,
            actions,
            nextEdits,
            sources
        };
    }

    private processJsonResponse(jsonResponse: any): AgentResponse {
        const actions: AgentAction[] = [];
        const nextEdits: NextEdit[] = [];
        const sources: string[] = [];
        let content = '';

        if (jsonResponse.action) {
            switch (jsonResponse.action) {
                case 'create_file':
                    actions.push({
                        type: 'create_file',
                        target: jsonResponse.file_path,
                        content: jsonResponse.content,
                        description: `Create file ${jsonResponse.file_path}`,
                        confidence: 0.95
                    });
                    content = `Creating file: ${jsonResponse.file_path}`;
                    break;

                case 'modify_file':
                    actions.push({
                        type: 'modify_file',
                        target: jsonResponse.file_path,
                        content: jsonResponse.content,
                        description: `Modify file ${jsonResponse.file_path}`,
                        confidence: 0.95
                    });
                    content = `Modifying file: ${jsonResponse.file_path}`;
                    break;

                case 'delete_file':
                    actions.push({
                        type: 'delete_file',
                        target: jsonResponse.file_path,
                        description: `Delete file ${jsonResponse.file_path}`,
                        confidence: 0.95
                    });
                    content = `Deleting file: ${jsonResponse.file_path}`;
                    break;

                case 'run_command':
                    actions.push({
                        type: 'run_command',
                        target: jsonResponse.command,
                        description: `Run command: ${jsonResponse.command}`,
                        confidence: 0.95
                    });
                    content = `Running command: ${jsonResponse.command}`;
                    break;

                case 'chat':
                    content = jsonResponse.message;
                    break;
            }
        }

        return {
            content,
            actions,
            nextEdits,
            sources
        };
    }

    async executeActions(actions: AgentAction[]): Promise<void> {
        for (const action of actions) {
            try {
                await this.toolRegistry.execute(action.type, action);
            } catch (error) {
                console.error(`Failed to execute action ${action.type}:`, error);
            }
        }
    }

    // Tool implementations
    private async createFile(action: AgentAction): Promise<void> {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder open');
        }

        const filePath = vscode.Uri.joinPath(workspaceFolder.uri, action.target);
        const content = action.content || '';
        
        await vscode.workspace.fs.writeFile(filePath, Buffer.from(content, 'utf8'));
        
        // Open the created file
        const document = await vscode.workspace.openTextDocument(filePath);
        await vscode.window.showTextDocument(document);
    }

    private async modifyFile(action: AgentAction): Promise<void> {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder open');
        }

        const filePath = vscode.Uri.joinPath(workspaceFolder.uri, action.target);

        try {
            // Write the new content to the file
            await vscode.workspace.fs.writeFile(filePath, Buffer.from(action.content || '', 'utf8'));

            // Open the modified file
            const document = await vscode.workspace.openTextDocument(filePath);
            await vscode.window.showTextDocument(document);

            console.log('Modified file:', action.target);
        } catch (error) {
            console.error('Error modifying file:', error);
            throw error;
        }
    }

    private async deleteFile(action: AgentAction): Promise<void> {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder open');
        }

        const filePath = vscode.Uri.joinPath(workspaceFolder.uri, action.target);

        try {
            await vscode.workspace.fs.delete(filePath);
            console.log('Deleted file:', action.target);
        } catch (error) {
            console.error('Error deleting file:', error);
            throw error;
        }
    }

    private async runCommand(action: AgentAction): Promise<void> {
        try {
            // Create a new terminal for the command
            const terminal = vscode.window.createTerminal({
                name: 'Augment Agent',
                cwd: vscode.workspace.workspaceFolders?.[0]?.uri.fsPath
            });

            // Show the terminal and run the command
            terminal.show();
            terminal.sendText(action.target);

            console.log('Running command:', action.target);
        } catch (error) {
            console.error('Error running command:', error);
            throw error;
        }
    }

    private async applyCode(action: AgentAction): Promise<void> {
        // Implementation for applying code to current location
        console.log('Applying code:', action.content);
    }

    private async getContext(action: AgentAction): Promise<any> {
        // Implementation for getting context
        return await this.contextEngine.gatherComprehensiveContext();
    }

    private getSelectedModel(): string {
        return vscode.workspace.getConfiguration('ollama-assistant').get('chatModel') || 'llama2';
    }

    private getOptimizedOptions(): any {
        return {
            temperature: 0.1,
            top_p: 0.9,
            num_ctx: 8192
        };
    }

    /**
     * NEW: Process chat request for natural conversation mode
     */
    async processChatRequest(
        message: string,
        includeContext: boolean = true,
        activeFile?: vscode.TextDocument,
        selection?: vscode.Selection
    ): Promise<ChatResponse> {
        try {
            // Gather comprehensive context
            const contextBundle = includeContext ?
                await this.contextEngine.gatherComprehensiveContext(activeFile, selection) :
                null;

            // Retrieve relevant memories for conversation context
            const relevantMemories = await this.memorySystem.getRelevantMemories(message, contextBundle);

            // Build chat-specific system prompt
            const systemPrompt = this.buildChatSystemPrompt(contextBundle, relevantMemories);

            // Build contextual message for chat
            const contextualMessage = this.buildChatContextualMessage(message, contextBundle);

            // Prepare conversation with context
            const messages = [
                { role: 'system', content: systemPrompt },
                ...this.conversationHistory,
                { role: 'user', content: contextualMessage }
            ];

            // Get AI response for chat
            let fullResponse = '';
            for await (const chunk of this.ollamaClient.chatStream({
                model: this.getSelectedModel(),
                messages,
                options: this.getOptimizedOptions()
            })) {
                if (chunk.message?.content) {
                    fullResponse += chunk.message.content;
                }
            }

            // Analyze if response suggests switching to agent mode
            const suggestAgentMode = this.shouldSuggestAgentMode(message, fullResponse);
            const agentModeReason = suggestAgentMode ? this.getAgentModeReason(message) : undefined;

            // Store in memory for persistence
            await this.memorySystem.updateMemories(message, {
                content: fullResponse,
                actions: [],
                nextEdits: [],
                sources: []
            }, contextBundle);

            // Add to conversation history
            this.conversationHistory.push(
                { role: 'user', content: message },
                { role: 'assistant', content: fullResponse }
            );

            return {
                content: fullResponse,
                suggestAgentMode,
                agentModeReason,
                type: 'chat'
            };

        } catch (error) {
            console.error('Chat processing error:', error);
            throw error;
        }
    }

    /**
     * Build system prompt for chat mode
     */
    private buildChatSystemPrompt(contextBundle: any, memories: AgentMemory[]): string {
        let prompt = `[CHAT MODE SYSTEM]
You are Augment Assistant in Chat Mode. Your primary goal is natural, conversational dialogue with the user about code and development topics.

CONVERSATIONAL APPROACH:
- Think step-by-step when responding: First acknowledge the user's question, then provide a concise and helpful answer using the provided context, and finally offer to switch to Agent Mode if applicable
- Engage in natural conversation - you are NOT executing commands or creating files in this mode
- Use a friendly, helpful tone that encourages dialogue and exploration of ideas
- Build on previous conversations and show that you remember the context

CORE RESPONSIBILITIES:
1. Answer questions about code, explain concepts, and provide guidance
2. Analyze code and suggest improvements when asked
3. Help with debugging and problem-solving through discussion
4. Provide explanations of programming concepts and best practices
5. Actively refer to CURRENT PROJECT CONTEXT and RELEVANT CONVERSATION HISTORY for highly specific, contextually aware responses

PROACTIVE AGENT MODE SUGGESTIONS (CRUCIAL):
When the user's query implies a coding task (keywords: "create", "build", "make", "add", "implement", "write", "generate", "fix", "update", "modify", "change", "refactor", "install", "run", "execute", "deploy", "setup", "configure"), you MUST:
1. Provide a helpful conversational response about the topic
2. Conclude with a clear, polite suggestion to switch to Agent Mode
3. Explain WHY Agent Mode is better for that specific request

Example: "This sounds like a task where Agent Mode could directly help you implement the code. Would you like me to switch modes and create those files for you?"

CONTEXT UTILIZATION:
- Leverage the provided project context to give specific, relevant advice
- Reference relevant memories from previous conversations to maintain continuity
- Go beyond generic responses by incorporating the user's specific codebase and project structure

CONTEXT INFORMATION:`;

        if (contextBundle) {
            prompt += `\n\nCURRENT PROJECT CONTEXT:\n${JSON.stringify(contextBundle, null, 2)}`;
        }

        if (memories.length > 0) {
            prompt += `\n\nRELEVANT CONVERSATION HISTORY:\n${memories.map(m => `- ${m.context}`).join('\n')}`;
        }

        return prompt;
    }

    /**
     * Build contextual message for chat mode
     */
    private buildChatContextualMessage(userMessage: string, contextBundle: any): string {
        let contextualMessage = '';

        if (contextBundle) {
            contextualMessage += '[CONTEXT]\n';

            // Active File Information
            if (contextBundle.activeFile) {
                contextualMessage += `Active File: ${contextBundle.activeFile.path}\n`;

                if (contextBundle.activeFile.selection) {
                    contextualMessage += `Selected code:\n${contextBundle.activeFile.selection}\n\n`;
                }
            }

            // Project Structure (limited for chat)
            if (contextBundle.workspace && contextBundle.workspace.fileStructure) {
                contextualMessage += 'Project Structure (key files):\n';
                contextBundle.workspace.fileStructure.slice(0, 10).forEach((file: string) => {
                    contextualMessage += `- ${file}\n`;
                });
            }

            contextualMessage += '[/CONTEXT]\n\n';
        }

        contextualMessage += `[USER MESSAGE]\n${userMessage}\n[/USER MESSAGE]`;

        return contextualMessage;
    }

    /**
     * Analyze if the user's request suggests switching to agent mode
     */
    private shouldSuggestAgentMode(userMessage: string, aiResponse: string): boolean {
        const codingKeywords = [
            'create', 'build', 'make', 'add', 'implement', 'write', 'generate',
            'fix', 'update', 'modify', 'change', 'refactor', 'install',
            'run', 'execute', 'deploy', 'setup', 'configure'
        ];

        const fileKeywords = [
            'file', 'component', 'function', 'class', 'module', 'package.json',
            'index.html', 'app.js', 'style.css', 'readme'
        ];

        const lowerMessage = userMessage.toLowerCase();

        // Check for direct coding action requests
        const hasCodingAction = codingKeywords.some(keyword => lowerMessage.includes(keyword));
        const hasFileReference = fileKeywords.some(keyword => lowerMessage.includes(keyword));

        // Check for command-like requests
        const hasCommandPattern = lowerMessage.includes('npm ') ||
                                 lowerMessage.includes('git ') ||
                                 lowerMessage.includes('yarn ') ||
                                 lowerMessage.includes('pip ');

        return (hasCodingAction && hasFileReference) || hasCommandPattern;
    }

    /**
     * Get reason for suggesting agent mode
     */
    private getAgentModeReason(userMessage: string): string {
        const lowerMessage = userMessage.toLowerCase();

        if (lowerMessage.includes('create') || lowerMessage.includes('build') || lowerMessage.includes('make')) {
            return 'This looks like a creation task that would benefit from Agent mode to actually create the files and code.';
        }

        if (lowerMessage.includes('fix') || lowerMessage.includes('update') || lowerMessage.includes('modify')) {
            return 'This appears to be a modification task. Agent mode can help execute the actual changes to your code.';
        }

        if (lowerMessage.includes('npm ') || lowerMessage.includes('install') || lowerMessage.includes('run')) {
            return 'This involves running commands. Agent mode can execute these commands in your terminal.';
        }

        return 'This looks like a coding task that would benefit from Agent mode for hands-on implementation.';
    }

    async dispose(): Promise<void> {
        await this.checkpoints.dispose();
        await this.memorySystem.dispose();
    }

    /**
     * Build system prompt for streaming command pipeline
     */
    private buildStreamingSystemPrompt(contextBundle: any, memories: AgentMemory[], mode: 'agent' | 'agent-auto' = 'agent'): string {
        let prompt = `[STREAMING COMMAND SYSTEM - ${mode.toUpperCase()} MODE]
You are a professional software engineering AI agent. You will receive a high-level request and break it down into a series of logical, executable commands that you stream one by one.

CRITICAL: Your response must be ONLY a sequence of valid JSON objects, one per line. No other text, explanations, or formatting.

COMMAND FORMAT:
{"action": "ACTION_TYPE", "file_path": "path/to/file", "content": "file content", "task_description": "What this step does", "is_complete": false}

ACTIONS AVAILABLE:
- chat: For planning, reasoning, and explaining your thought process
- create_file: Create a new file with content
- modify_file: Modify existing file content
- run_command: Execute shell/terminal commands (will be interactive in UI)
- apply_code: Apply code changes to current file
- task_complete: Mark entire task as finished (set is_complete: true)

MANDATORY EXECUTION FLOW:
1. **ALWAYS START** with {"action": "chat", "task_description": "..."} that outlines your complete high-level plan based on analysis of the user's request and codebase context
2. **VERBOSE REASONING**: For every subsequent command, the task_description must include your reasoning BEFORE stating the action
3. **STEP-BY-STEP THINKING**: Make your internal thought process visible through detailed task_description fields

${mode === 'agent-auto' ? `
AUTONOMOUS MODE BEHAVIOR:
- Execute commands decisively without asking for confirmation
- Focus on efficient task completion
- Minimize conversational elements, maximize action
- Still provide reasoning in task_description but be more direct
` : `
GUIDED MODE BEHAVIOR:
- Provide detailed explanations of your reasoning
- Show your thinking process clearly
- Make the user feel involved in the decision-making
`}

TASK_DESCRIPTION REQUIREMENTS:
- Precede each action with WHY you're taking that step
- Link each step back to the overall plan
- Use natural language that shows your reasoning process
- Example: "Based on the project structure analysis, I need to create the package.json first because it defines the project dependencies and scripts that other files will rely on."

TERMINAL COMMANDS:
- Use {"action": "run_command", "command": "npm install", "task_description": "..."} for executable commands
- These will appear as interactive "Run in Terminal" buttons in the UI
- Explain why each command is necessary in the task_description

EXAMPLE VERBOSE EXECUTION for "Create a React app":
{"action": "chat", "task_description": "I'll create a React application for you. My plan is: 1) Set up the project structure with package.json, 2) Create the main App component, 3) Add an index.html file, 4) Install dependencies. This approach ensures we have a solid foundation before adding components.", "is_complete": false}
{"action": "create_file", "file_path": "package.json", "content": "{...}", "task_description": "Creating package.json first because it defines the project metadata, dependencies, and build scripts that the entire project relies on. This establishes the foundation for our React app.", "is_complete": false}
{"action": "create_file", "file_path": "src/App.tsx", "content": "...", "task_description": "Now creating the main App component. I'm using TypeScript for better type safety and including basic React hooks. This will serve as the entry point for our application logic.", "is_complete": false}
{"action": "run_command", "command": "npm install", "task_description": "Installing the dependencies defined in package.json. This command will download React, TypeScript, and all other required packages to make the project functional.", "is_complete": false}

CONTEXT INFORMATION:`;

        if (contextBundle) {
            prompt += `\n\nCURRENT PROJECT CONTEXT:\n${JSON.stringify(contextBundle, null, 2)}`;
        }

        if (memories.length > 0) {
            prompt += `\n\nRELEVANT MEMORIES:\n${memories.map(m => `- ${m.context}`).join('\n')}`;
        }

        return prompt;
    }

    /**
     * Extract complete JSON commands from streaming buffer
     */
    private extractCommandsFromBuffer(buffer: string, forceFinal: boolean = false): CommandExtractionResult {
        const commands: StreamCommand[] = [];
        let remaining = buffer;

        // Look for complete JSON objects
        let braceCount = 0;
        let inString = false;
        let escaped = false;
        let jsonStart = -1;

        for (let i = 0; i < buffer.length; i++) {
            const char = buffer[i];

            if (escaped) {
                escaped = false;
                continue;
            }

            if (char === '\\' && inString) {
                escaped = true;
                continue;
            }

            if (char === '"') {
                inString = !inString;
                continue;
            }

            if (!inString) {
                if (char === '{') {
                    if (braceCount === 0) {
                        jsonStart = i;
                    }
                    braceCount++;
                } else if (char === '}') {
                    braceCount--;

                    if (braceCount === 0 && jsonStart !== -1) {
                        // Found complete JSON object
                        const jsonStr = buffer.substring(jsonStart, i + 1);
                        try {
                            const command = JSON.parse(jsonStr) as StreamCommand;
                            commands.push(command);
                            remaining = buffer.substring(i + 1);
                            jsonStart = -1;
                        } catch (error) {
                            // Invalid JSON, continue searching
                        }
                    }
                }
            }
        }

        // If forcing final extraction and we have partial JSON, try to parse it
        if (forceFinal && jsonStart !== -1) {
            const partialJson = buffer.substring(jsonStart);
            try {
                const command = JSON.parse(partialJson) as StreamCommand;
                commands.push(command);
                remaining = '';
            } catch (error) {
                // Could not parse partial JSON
            }
        }

        return { complete: commands, remaining };
    }

    /**
     * Execute a single stream command
     */
    private async executeStreamCommand(command: StreamCommand): Promise<void> {
        try {
            switch (command.action) {
                case 'create_file':
                    if (command.file_path && command.content !== undefined) {
                        await this.createFile({
                            type: 'create_file',
                            target: command.file_path,
                            content: command.content,
                            description: command.task_description,
                            confidence: 0.9
                        });
                    }
                    break;

                case 'modify_file':
                    if (command.file_path && command.content !== undefined) {
                        await this.modifyFile({
                            type: 'modify_file',
                            target: command.file_path,
                            content: command.content,
                            description: command.task_description,
                            confidence: 0.9
                        });
                    }
                    break;

                case 'run_command':
                    if (command.command) {
                        await this.runCommand({
                            type: 'run_command',
                            target: command.command,
                            description: command.task_description,
                            confidence: 0.9
                        });
                    }
                    break;

                case 'apply_code':
                    if (command.code) {
                        await this.applyCode({
                            type: 'apply_code',
                            target: command.file_path || 'current_file',
                            content: command.code,
                            description: command.task_description,
                            confidence: 0.9
                        });
                    }
                    break;

                case 'delete_file':
                    if (command.file_path) {
                        await this.deleteFile({
                            type: 'delete_file',
                            target: command.file_path,
                            description: command.task_description,
                            confidence: 0.9
                        });
                    }
                    break;

                case 'chat':
                case 'task_complete':
                    // These are informational commands, no action needed
                    break;

                default:
                    console.warn(`Unknown stream command action: ${command.action}`);
            }
        } catch (error) {
            console.error(`Error executing stream command ${command.action}:`, error);
            throw error;
        }
    }
}
