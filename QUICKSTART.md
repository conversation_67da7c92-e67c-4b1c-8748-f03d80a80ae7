# Quick Start Guide

Get your Ollama Code Assistant up and running in minutes!

## Prerequisites

1. **Ollama Installation**: Download and install Ollama from [https://ollama.ai](https://ollama.ai)
2. **VS Code**: Version 1.80.0 or higher
3. **Node.js**: Version 16 or higher (for development)

## Step 1: Install and Start Ollama

### Windows
```powershell
# Download from https://ollama.ai and run the installer
# Or use winget
winget install Ollama.Ollama
```

### macOS
```bash
# Download from https://ollama.ai
# Or use Homebrew
brew install ollama
```

### Linux
```bash
curl -fsSL https://ollama.ai/install.sh | sh
```

### Start Ollama Server
```bash
ollama serve
```

## Step 2: Install AI Models

Install at least one model for code assistance:

```bash
# Recommended for code completion (fast, good quality)
ollama pull codellama:7b

# Alternative models
ollama pull mistral:7b      # Good general purpose
ollama pull llama2:7b       # Good for chat
ollama pull codellama:13b   # Better quality, slower
```

Verify installation:
```bash
ollama list
```

## Step 3: Set Up the Extension

### Option A: Development Mode (Recommended for now)

1. **Clone and Build**:
   ```bash
   git clone <your-repo-url>
   cd ollama-code-assistant
   npm install
   npm run compile
   ```

2. **Launch in VS Code**:
   - Open the project folder in VS Code
   - Press `F5` to launch Extension Development Host
   - A new VS Code window will open with the extension loaded

### Option B: Package and Install

1. **Package the Extension**:
   ```bash
   npm run package
   ```

2. **Install the .vsix file**:
   ```bash
   code --install-extension ollama-code-assistant-*.vsix
   ```

## Step 4: Configure the Extension

1. **Open Settings** (Ctrl+,)
2. **Search for "Ollama Assistant"**
3. **Configure**:
   - **Server URL**: `http://localhost:11434` (default)
   - **Default Model**: Select your preferred model for completions
   - **Chat Model**: Select your preferred model for chat

### Quick Configuration via Command Palette

1. Press `Ctrl+Shift+P`
2. Type "Ollama Assistant: Select Model"
3. Choose your model from the list

## Step 5: Test the Extension

### Test Chat Interface

1. **Open Chat**:
   - Click the Ollama Assistant icon in the sidebar
   - Or press `Ctrl+Shift+P` and run "Ollama Assistant: Open Chat"

2. **Try a Simple Question**:
   ```
   Hello! Can you help me with coding?
   ```

3. **Test with Code Context**:
   - Open a code file
   - Select some code
   - In chat, ask: "Explain this code"
   - Make sure "Include current file context" is checked

### Test Code Completions

1. **Open a Code File** (e.g., `.js`, `.ts`, `.py`)
2. **Start Typing**:
   ```javascript
   function calculateSum(a, b) {
       // Start typing here and wait for suggestions
   ```
3. **Accept Completions**: Press `Tab` to accept suggestions

## Troubleshooting

### Extension Not Loading
- Check VS Code version (must be 1.80.0+)
- Check the Output panel for errors
- Restart VS Code

### Can't Connect to Ollama
```bash
# Check if Ollama is running
curl http://localhost:11434/api/tags

# If not working, restart Ollama
ollama serve
```

### No Models Available
```bash
# List installed models
ollama list

# Install a model if none are available
ollama pull codellama:7b
```

### Slow Completions
- Try a smaller model (7B instead of 13B)
- Increase completion delay in settings
- Check system resources (RAM usage)

### Poor Completion Quality
- Try different models
- Ensure relevant files are open in VS Code
- Check context settings

## Usage Tips

### For Best Code Completions
1. **Keep related files open** in VS Code tabs
2. **Use descriptive variable names** and comments
3. **Start with clear function signatures**
4. **Be patient** - local models take time to process

### For Effective Chat
1. **Be specific** in your questions
2. **Include context** when asking about code
3. **Ask follow-up questions** to clarify
4. **Use code blocks** in your questions when relevant

### Performance Optimization
1. **Model Selection**:
   - 7B models: Faster, good for most tasks
   - 13B models: Better quality, slower
   - 34B+ models: Best quality, requires powerful hardware

2. **System Requirements**:
   - 7B model: 8GB+ RAM
   - 13B model: 16GB+ RAM
   - 34B model: 32GB+ RAM

## Example Workflows

### Code Review
1. Select problematic code
2. Open chat with context enabled
3. Ask: "What potential issues do you see in this code?"

### Documentation
1. Select a function
2. Ask: "Write JSDoc comments for this function"

### Refactoring
1. Select code to refactor
2. Ask: "How can I make this code more readable and efficient?"

### Learning
1. Select unfamiliar code
2. Ask: "Explain how this code works step by step"

## Next Steps

- Explore the [Development Guide](DEVELOPMENT.md) for customization
- Check out advanced features in the [README](README.md)
- Report issues or request features on GitHub

## Support

- **Issues**: GitHub Issues
- **Documentation**: README.md and DEVELOPMENT.md
- **Community**: GitHub Discussions

---

**Happy Coding with Local AI! 🚀**
