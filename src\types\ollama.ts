export interface OllamaModel {
    name: string;
    modified_at: string;
    size: number;
    digest: string;
    details?: {
        format: string;
        family: string;
        families: string[];
        parameter_size: string;
        quantization_level: string;
    };
}

export interface ChatMessage {
    role: 'system' | 'user' | 'assistant';
    content: string;
    images?: string[];
}

export interface ChatRequest {
    model: string;
    messages: ChatMessage[];
    stream?: boolean;
    format?: 'json';
    options?: ModelOptions;
    keep_alive?: string;
}

export interface ChatResponse {
    model: string;
    created_at: string;
    message: ChatMessage;
    done: boolean;
    total_duration?: number;
    load_duration?: number;
    prompt_eval_count?: number;
    prompt_eval_duration?: number;
    eval_count?: number;
    eval_duration?: number;
}

export interface CompletionRequest {
    model: string;
    prompt: string;
    suffix?: string;
    images?: string[];
    format?: 'json';
    options?: ModelOptions;
    system?: string;
    template?: string;
    context?: number[];
    stream?: boolean;
    raw?: boolean;
    keep_alive?: string;
}

export interface CompletionResponse {
    model: string;
    created_at: string;
    response: string;
    done: boolean;
    context?: number[];
    total_duration?: number;
    load_duration?: number;
    prompt_eval_count?: number;
    prompt_eval_duration?: number;
    eval_count?: number;
    eval_duration?: number;
}

export interface ModelOptions {
    // Sampling parameters
    temperature?: number;
    top_k?: number;
    top_p?: number;
    repeat_penalty?: number;
    seed?: number;
    
    // Generation parameters
    num_predict?: number;
    num_ctx?: number;
    num_batch?: number;
    num_gqa?: number;
    num_gpu?: number;
    main_gpu?: number;
    low_vram?: boolean;
    f16_kv?: boolean;
    logits_all?: boolean;
    vocab_only?: boolean;
    use_mmap?: boolean;
    use_mlock?: boolean;
    embedding_only?: boolean;
    rope_frequency_base?: number;
    rope_frequency_scale?: number;
    num_thread?: number;
    
    // Stop sequences
    stop?: string[];
}

export interface OllamaError {
    error: string;
}

export interface ModelListResponse {
    models: OllamaModel[];
}

export interface ContextBundle {
    currentFile?: {
        path: string;
        content: string;
        language: string;
        selection?: {
            start: number;
            end: number;
            text: string;
        };
        cursorPosition?: {
            line: number;
            character: number;
        };
    };
    relatedFiles?: Array<{
        path: string;
        content: string;
        relevanceScore: number;
        reason: string;
    }>;
    projectContext?: {
        packageJson?: any;
        tsConfig?: any;
        gitignore?: string[];
        fileStructure?: string[];
    };
    conversationHistory?: ChatMessage[];
}
