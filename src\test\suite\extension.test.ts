import * as assert from 'assert';
import * as vscode from 'vscode';
import { OllamaClient } from '../../services/ollamaClient';
import { ConfigurationManager } from '../../services/configurationManager';

suite('Extension Test Suite', () => {
    vscode.window.showInformationMessage('Start all tests.');

    test('Extension should be present', () => {
        // List all extensions to debug
        const allExtensions = vscode.extensions.all.map(ext => ext.id);
        console.log('All loaded extensions:', allExtensions);

        // Look for our extension
        const ourExtension = vscode.extensions.all.find(ext =>
            ext.id.includes('ollama') || ext.packageJSON?.name === 'ollama-code-assistant'
        );

        assert.ok(ourExtension, `Extension should be loaded. Available extensions: ${allExtensions.join(', ')}`);
    });

    test('Configuration Manager should work', () => {
        const configManager = new ConfigurationManager();
        const serverUrl = configManager.getServerUrl();
        assert.ok(serverUrl);
        assert.strictEqual(typeof serverUrl, 'string');
    });

    test('Ollama Client should initialize', () => {
        const configManager = new ConfigurationManager();
        const ollamaClient = new OllamaClient(configManager);
        assert.ok(ollamaClient);
    });

    test('Commands should be registered', async () => {
        const commands = await vscode.commands.getCommands(true);
        const ollamaCommands = commands.filter(cmd => cmd.startsWith('ollama-assistant.'));
        
        assert.ok(ollamaCommands.includes('ollama-assistant.openChat'));
        assert.ok(ollamaCommands.includes('ollama-assistant.selectModel'));
        assert.ok(ollamaCommands.includes('ollama-assistant.refreshModels'));
    });
});
