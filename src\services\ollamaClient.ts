import axios, { AxiosInstance, AxiosResponse } from 'axios';
import * as vscode from 'vscode';
import { 
    OllamaModel, 
    ChatRequest, 
    ChatResponse, 
    CompletionRequest, 
    CompletionResponse,
    ModelListResponse,
    OllamaError 
} from '../types/ollama';
import { ConfigurationManager } from './configurationManager';

export class OllamaClient {
    private httpClient: AxiosInstance;
    private models: OllamaModel[] = [];
    private isConnected: boolean = false;

    constructor(private configManager: ConfigurationManager) {
        const serverUrl = this.configManager.getServerUrl();
        
        this.httpClient = axios.create({
            baseURL: serverUrl,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
            },
        });

        // Add response interceptor for error handling
        this.httpClient.interceptors.response.use(
            (response) => response,
            (error) => {
                console.error('Ollama API Error:', error);
                if (error.code === 'ECONNREFUSED') {
                    throw new Error('Cannot connect to Ollama server. Please ensure Ollama is running.');
                }
                throw error;
            }
        );
    }

    async initialize(): Promise<void> {
        try {
            await this.ping();
            await this.refreshModels();
            this.isConnected = true;
        } catch (error) {
            this.isConnected = false;
            throw error;
        }
    }

    async ping(): Promise<boolean> {
        try {
            const response = await this.httpClient.get('/api/tags');
            return response.status === 200;
        } catch (error) {
            return false;
        }
    }

    async refreshModels(): Promise<void> {
        try {
            const response: AxiosResponse<ModelListResponse> = await this.httpClient.get('/api/tags');
            this.models = response.data.models || [];
        } catch (error) {
            console.error('Failed to refresh models:', error);
            throw new Error('Failed to fetch available models from Ollama');
        }
    }

    getAvailableModels(): OllamaModel[] {
        return this.models;
    }

    async *chatStream(request: ChatRequest): AsyncGenerator<ChatResponse, void, unknown> {
        const streamRequest = { ...request, stream: true };
        
        try {
            const response = await this.httpClient.post('/api/chat', streamRequest, {
                responseType: 'stream',
                timeout: 0, // No timeout for streaming
            });

            let buffer = '';
            
            for await (const chunk of response.data) {
                buffer += chunk.toString();
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';

                for (const line of lines) {
                    if (line.trim()) {
                        try {
                            const data: ChatResponse = JSON.parse(line);
                            yield data;
                        } catch (parseError) {
                            console.warn('Failed to parse streaming response:', line);
                        }
                    }
                }
            }

            // Process any remaining buffer
            if (buffer.trim()) {
                try {
                    const data: ChatResponse = JSON.parse(buffer);
                    yield data;
                } catch (parseError) {
                    console.warn('Failed to parse final streaming response:', buffer);
                }
            }
        } catch (error) {
            console.error('Chat stream error:', error);
            throw new Error('Failed to stream chat response from Ollama');
        }
    }

    async chat(request: ChatRequest): Promise<ChatResponse> {
        const nonStreamRequest = { ...request, stream: false };
        
        try {
            const response: AxiosResponse<ChatResponse> = await this.httpClient.post('/api/chat', nonStreamRequest);
            return response.data;
        } catch (error) {
            console.error('Chat error:', error);
            throw new Error('Failed to get chat response from Ollama');
        }
    }

    async *completionStream(request: CompletionRequest): AsyncGenerator<CompletionResponse, void, unknown> {
        const streamRequest = { ...request, stream: true };
        
        try {
            const response = await this.httpClient.post('/api/generate', streamRequest, {
                responseType: 'stream',
                timeout: 0,
            });

            let buffer = '';
            
            for await (const chunk of response.data) {
                buffer += chunk.toString();
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';

                for (const line of lines) {
                    if (line.trim()) {
                        try {
                            const data: CompletionResponse = JSON.parse(line);
                            yield data;
                        } catch (parseError) {
                            console.warn('Failed to parse completion streaming response:', line);
                        }
                    }
                }
            }

            if (buffer.trim()) {
                try {
                    const data: CompletionResponse = JSON.parse(buffer);
                    yield data;
                } catch (parseError) {
                    console.warn('Failed to parse final completion streaming response:', buffer);
                }
            }
        } catch (error) {
            console.error('Completion stream error:', error);
            throw new Error('Failed to stream completion response from Ollama');
        }
    }

    async completion(request: CompletionRequest): Promise<CompletionResponse> {
        const nonStreamRequest = { ...request, stream: false };
        
        try {
            const response: AxiosResponse<CompletionResponse> = await this.httpClient.post('/api/generate', nonStreamRequest);
            return response.data;
        } catch (error) {
            console.error('Completion error:', error);
            throw new Error('Failed to get completion response from Ollama');
        }
    }

    isServerConnected(): boolean {
        return this.isConnected;
    }

    dispose(): void {
        // Cleanup any resources if needed
        this.isConnected = false;
    }
}
