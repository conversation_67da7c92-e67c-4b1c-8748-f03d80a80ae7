{"version": "2.0.0", "tasks": [{"type": "npm", "script": "compile", "group": "build", "presentation": {"panel": "shared", "reveal": "silent", "clear": false}, "problemMatcher": ["$tsc"]}, {"type": "npm", "script": "watch", "group": "build", "presentation": {"panel": "shared", "reveal": "never", "clear": false}, "isBackground": true, "problemMatcher": ["$tsc-watch"]}, {"type": "npm", "script": "lint", "group": "test", "presentation": {"panel": "shared", "reveal": "silent", "clear": false}}, {"type": "npm", "script": "test", "group": "test", "presentation": {"panel": "shared", "reveal": "always", "clear": false}}]}