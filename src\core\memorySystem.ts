import * as vscode from 'vscode';
import * as path from 'path';
import { AgentMemory, AgentResponse } from './augmentAgent';

export interface CodeStyle {
    indentation: 'spaces' | 'tabs';
    indentSize: number;
    quotes: 'single' | 'double';
    semicolons: boolean;
    trailingCommas: boolean;
    bracketSpacing: boolean;
    arrowParens: 'avoid' | 'always';
    patterns: string[];
}

export interface UserPreferences {
    preferredLanguages: string[];
    codingStyle: CodeStyle;
    commonPatterns: string[];
    frequentCommands: string[];
    workflowPreferences: any;
}

export interface ProjectMemory {
    projectPath: string;
    architecture: string[];
    conventions: string[];
    commonFiles: string[];
    dependencies: string[];
    buildCommands: string[];
    testCommands: string[];
}

export interface ConversationMemory {
    id: string;
    timestamp: Date;
    context: string;
    userIntent: string;
    agentResponse: string;
    codeGenerated: string[];
    filesModified: string[];
    success: boolean;
    feedback?: string;
}

export class MemorySystem {
    private memories: Map<string, AgentMemory> = new Map();
    private conversationHistory: ConversationMemory[] = [];
    private userPreferences: UserPreferences;
    private projectMemory: ProjectMemory;
    private memoryFile: vscode.Uri;

    constructor(private context: vscode.ExtensionContext) {
        this.memoryFile = vscode.Uri.joinPath(context.globalStorageUri, 'agent-memories.json');
        this.userPreferences = this.initializeUserPreferences();
        this.projectMemory = this.initializeProjectMemory();
        this.loadMemories();
    }

    private initializeUserPreferences(): UserPreferences {
        return {
            preferredLanguages: ['typescript', 'javascript', 'python'],
            codingStyle: {
                indentation: 'spaces',
                indentSize: 4,
                quotes: 'single',
                semicolons: true,
                trailingCommas: true,
                bracketSpacing: true,
                arrowParens: 'avoid',
                patterns: []
            },
            commonPatterns: [],
            frequentCommands: [],
            workflowPreferences: {}
        };
    }

    private initializeProjectMemory(): ProjectMemory {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        return {
            projectPath: workspaceFolder?.uri.fsPath || '',
            architecture: [],
            conventions: [],
            commonFiles: [],
            dependencies: [],
            buildCommands: [],
            testCommands: []
        };
    }

    async getRelevantMemories(query: string, contextBundle?: any): Promise<AgentMemory[]> {
        const relevantMemories: AgentMemory[] = [];
        const queryLower = query.toLowerCase();

        // Get memories based on semantic similarity
        for (const memory of this.memories.values()) {
            const relevanceScore = this.calculateRelevance(memory, queryLower, contextBundle);
            if (relevanceScore > 0.3) {
                relevantMemories.push(memory);
            }
        }

        // Sort by relevance and recency
        relevantMemories.sort((a, b) => {
            const scoreA = this.calculateRelevance(a, queryLower, contextBundle);
            const scoreB = this.calculateRelevance(b, queryLower, contextBundle);
            
            if (Math.abs(scoreA - scoreB) < 0.1) {
                // If scores are similar, prefer more recent memories
                return b.timestamp.getTime() - a.timestamp.getTime();
            }
            
            return scoreB - scoreA;
        });

        // Return top 5 most relevant memories
        return relevantMemories.slice(0, 5);
    }

    private calculateRelevance(memory: AgentMemory, query: string, contextBundle?: any): number {
        let score = 0;

        // Text similarity
        const memoryText = memory.context.toLowerCase();
        const queryWords = query.split(' ').filter(word => word.length > 2);
        
        for (const word of queryWords) {
            if (memoryText.includes(word)) {
                score += 0.2;
            }
        }

        // Pattern matching
        for (const pattern of memory.patterns) {
            if (query.includes(pattern.toLowerCase())) {
                score += 0.3;
            }
        }

        // Context similarity
        if (contextBundle?.activeFile) {
            const currentLanguage = contextBundle.activeFile.language;
            if (memory.context.includes(currentLanguage)) {
                score += 0.2;
            }
        }

        // Recency bonus (more recent memories get higher scores)
        const daysSinceCreated = (Date.now() - memory.timestamp.getTime()) / (1000 * 60 * 60 * 24);
        const recencyBonus = Math.max(0, 0.3 - (daysSinceCreated * 0.01));
        score += recencyBonus;

        return Math.min(score, 1.0);
    }

    async updateMemories(
        userMessage: string, 
        agentResponse: AgentResponse, 
        contextBundle?: any
    ): Promise<void> {
        // Create conversation memory
        const conversationMemory: ConversationMemory = {
            id: this.generateId(),
            timestamp: new Date(),
            context: contextBundle?.activeFile?.path || 'general',
            userIntent: this.extractIntent(userMessage),
            agentResponse: agentResponse.content,
            codeGenerated: this.extractCodeFromResponse(agentResponse.content),
            filesModified: agentResponse.actions.map(action => action.target),
            success: true // Will be updated based on user feedback
        };

        this.conversationHistory.push(conversationMemory);

        // Update or create agent memory
        await this.updateAgentMemory(userMessage, agentResponse, contextBundle);

        // Update user preferences based on interaction
        await this.updateUserPreferences(userMessage, agentResponse, contextBundle);

        // Update project memory
        await this.updateProjectMemory(agentResponse, contextBundle);

        // Save memories to disk
        await this.saveMemories();
    }

    private async updateAgentMemory(
        userMessage: string, 
        agentResponse: AgentResponse, 
        contextBundle?: any
    ): Promise<void> {
        const memoryKey = this.generateMemoryKey(userMessage, contextBundle);
        
        let memory = this.memories.get(memoryKey);
        if (!memory) {
            memory = {
                id: memoryKey,
                timestamp: new Date(),
                context: this.buildMemoryContext(userMessage, contextBundle),
                codeStyle: this.extractCodeStyle(agentResponse.content),
                patterns: this.extractPatterns(userMessage, agentResponse.content),
                preferences: {}
            };
        } else {
            // Update existing memory
            memory.timestamp = new Date();
            memory.patterns = [...new Set([...memory.patterns, ...this.extractPatterns(userMessage, agentResponse.content)])];
        }

        this.memories.set(memoryKey, memory);
    }

    private generateMemoryKey(userMessage: string, contextBundle?: any): string {
        const context = contextBundle?.activeFile?.language || 'general';
        const intent = this.extractIntent(userMessage);
        return `${context}_${intent}_${Date.now()}`;
    }

    private buildMemoryContext(userMessage: string, contextBundle?: any): string {
        let context = `User requested: ${userMessage}`;
        
        if (contextBundle?.activeFile) {
            context += ` in ${contextBundle.activeFile.language} file: ${contextBundle.activeFile.path}`;
        }
        
        if (contextBundle?.workspace) {
            context += ` (Project: ${path.basename(contextBundle.workspace.rootPath)})`;
        }

        return context;
    }

    private extractIntent(userMessage: string): string {
        const message = userMessage.toLowerCase();
        
        if (message.includes('create') || message.includes('generate')) {
            return 'create';
        } else if (message.includes('fix') || message.includes('debug') || message.includes('error')) {
            return 'debug';
        } else if (message.includes('refactor') || message.includes('improve') || message.includes('optimize')) {
            return 'refactor';
        } else if (message.includes('explain') || message.includes('understand') || message.includes('how')) {
            return 'explain';
        } else if (message.includes('test') || message.includes('unit test')) {
            return 'test';
        } else if (message.includes('document') || message.includes('comment')) {
            return 'document';
        }
        
        return 'general';
    }

    private extractCodeFromResponse(response: string): string[] {
        const codeBlocks: string[] = [];
        const codeBlockRegex = /```[\w]*\n([\s\S]*?)\n```/g;
        let match;
        
        while ((match = codeBlockRegex.exec(response)) !== null) {
            codeBlocks.push(match[1]);
        }
        
        return codeBlocks;
    }

    private extractCodeStyle(response: string): any {
        // Analyze code style from the response
        const codeBlocks = this.extractCodeFromResponse(response);
        const style: any = {};
        
        for (const code of codeBlocks) {
            // Detect indentation
            const lines = code.split('\n').filter(line => line.trim().length > 0);
            for (const line of lines) {
                const leadingWhitespace = line.match(/^(\s*)/)?.[1] || '';
                if (leadingWhitespace.length > 0) {
                    if (leadingWhitespace.includes('\t')) {
                        style.indentation = 'tabs';
                    } else {
                        style.indentation = 'spaces';
                        style.indentSize = leadingWhitespace.length;
                    }
                    break;
                }
            }
            
            // Detect quote style
            const singleQuotes = (code.match(/'/g) || []).length;
            const doubleQuotes = (code.match(/"/g) || []).length;
            if (singleQuotes > doubleQuotes) {
                style.quotes = 'single';
            } else if (doubleQuotes > singleQuotes) {
                style.quotes = 'double';
            }
            
            // Detect semicolon usage
            const semicolons = (code.match(/;/g) || []).length;
            const statements = (code.match(/\n/g) || []).length;
            style.semicolons = semicolons > statements * 0.5;
        }
        
        return style;
    }

    private extractPatterns(userMessage: string, response: string): string[] {
        const patterns: string[] = [];
        
        // Extract common programming patterns
        const codeBlocks = this.extractCodeFromResponse(response);
        for (const code of codeBlocks) {
            // Function patterns
            if (code.includes('function') || code.includes('=>')) {
                patterns.push('function_definition');
            }
            
            // Class patterns
            if (code.includes('class ')) {
                patterns.push('class_definition');
            }
            
            // Import patterns
            if (code.includes('import ') || code.includes('require(')) {
                patterns.push('imports');
            }
            
            // Error handling patterns
            if (code.includes('try') || code.includes('catch') || code.includes('throw')) {
                patterns.push('error_handling');
            }
            
            // Async patterns
            if (code.includes('async') || code.includes('await') || code.includes('Promise')) {
                patterns.push('async_programming');
            }
        }
        
        return patterns;
    }

    private async updateUserPreferences(
        userMessage: string, 
        agentResponse: AgentResponse, 
        contextBundle?: any
    ): Promise<void> {
        // Update preferred languages
        if (contextBundle?.activeFile?.language) {
            const language = contextBundle.activeFile.language;
            if (!this.userPreferences.preferredLanguages.includes(language)) {
                this.userPreferences.preferredLanguages.push(language);
            }
        }

        // Update coding style preferences
        const extractedStyle = this.extractCodeStyle(agentResponse.content);
        Object.assign(this.userPreferences.codingStyle, extractedStyle);

        // Update common patterns
        const patterns = this.extractPatterns(userMessage, agentResponse.content);
        for (const pattern of patterns) {
            if (!this.userPreferences.commonPatterns.includes(pattern)) {
                this.userPreferences.commonPatterns.push(pattern);
            }
        }
    }

    private async updateProjectMemory(agentResponse: AgentResponse, contextBundle?: any): Promise<void> {
        // Update project-specific memories
        if (contextBundle?.workspace) {
            this.projectMemory.projectPath = contextBundle.workspace.rootPath;
            
            // Update dependencies
            if (contextBundle.dependencies) {
                for (const dep of contextBundle.dependencies) {
                    if (!this.projectMemory.dependencies.includes(dep.name)) {
                        this.projectMemory.dependencies.push(dep.name);
                    }
                }
            }
            
            // Update common files
            if (contextBundle.activeFile) {
                const fileName = path.basename(contextBundle.activeFile.path);
                if (!this.projectMemory.commonFiles.includes(fileName)) {
                    this.projectMemory.commonFiles.push(fileName);
                }
            }
        }
    }

    private async loadMemories(): Promise<void> {
        try {
            await vscode.workspace.fs.stat(this.memoryFile);
            const content = await vscode.workspace.fs.readFile(this.memoryFile);
            const data = JSON.parse(content.toString());
            
            if (data.memories) {
                for (const [key, memory] of Object.entries(data.memories)) {
                    const memoryObj = memory as any;
                    memoryObj.timestamp = new Date(memoryObj.timestamp);
                    this.memories.set(key, memoryObj);
                }
            }
            
            if (data.userPreferences) {
                this.userPreferences = { ...this.userPreferences, ...data.userPreferences };
            }
            
            if (data.projectMemory) {
                this.projectMemory = { ...this.projectMemory, ...data.projectMemory };
            }
            
            if (data.conversationHistory) {
                this.conversationHistory = data.conversationHistory.map((conv: any) => ({
                    ...conv,
                    timestamp: new Date(conv.timestamp)
                }));
            }
        } catch (error) {
            // Memory file doesn't exist yet, start fresh
            console.log('No existing memories found, starting fresh');
        }
    }

    private async saveMemories(): Promise<void> {
        try {
            await vscode.workspace.fs.createDirectory(this.context.globalStorageUri);
            
            const data = {
                memories: Object.fromEntries(this.memories),
                userPreferences: this.userPreferences,
                projectMemory: this.projectMemory,
                conversationHistory: this.conversationHistory.slice(-100) // Keep last 100 conversations
            };
            
            await vscode.workspace.fs.writeFile(
                this.memoryFile, 
                Buffer.from(JSON.stringify(data, null, 2), 'utf8')
            );
        } catch (error) {
            console.error('Failed to save memories:', error);
        }
    }

    private generateId(): string {
        return Math.random().toString(36).substr(2, 9);
    }

    getUserPreferences(): UserPreferences {
        return this.userPreferences;
    }

    getProjectMemory(): ProjectMemory {
        return this.projectMemory;
    }

    async addMemory(memory: AgentMemory): Promise<void> {
        this.memories.set(memory.id, memory);
        await this.saveMemories();
    }

    async removeMemory(id: string): Promise<void> {
        this.memories.delete(id);
        await this.saveMemories();
    }

    async clearMemories(): Promise<void> {
        this.memories.clear();
        this.conversationHistory = [];
        await this.saveMemories();
    }

    async dispose(): Promise<void> {
        await this.saveMemories();
    }
}
