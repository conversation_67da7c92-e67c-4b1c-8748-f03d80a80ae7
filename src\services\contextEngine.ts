import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { ContextBundle } from '../types/ollama';

export class ContextEngine {
    private readonly MAX_FILE_SIZE = 1024 * 1024; // 1MB
    private readonly SUPPORTED_EXTENSIONS = new Set([
        '.ts', '.js', '.tsx', '.jsx', '.py', '.java', '.cpp', '.c', '.h', '.hpp',
        '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala', '.clj',
        '.html', '.css', '.scss', '.less', '.vue', '.svelte', '.json', '.xml',
        '.yaml', '.yml', '.toml', '.ini', '.cfg', '.conf', '.md', '.txt'
    ]);

    async gatherContext(
        document: vscode.TextDocument,
        position: vscode.Position,
        selection?: vscode.Selection
    ): Promise<ContextBundle> {
        const context: ContextBundle = {};

        // Current file context
        context.currentFile = await this.getCurrentFileContext(document, position, selection);

        // Related files context
        context.relatedFiles = await this.getRelatedFiles(document);

        // Project context
        context.projectContext = await this.getProjectContext();

        return context;
    }

    private async getCurrentFileContext(
        document: vscode.TextDocument,
        position: vscode.Position,
        selection?: vscode.Selection
    ) {
        const content = document.getText();
        const language = document.languageId;
        const filePath = vscode.workspace.asRelativePath(document.uri);

        const currentFile: any = {
            path: filePath,
            content,
            language,
            cursorPosition: {
                line: position.line,
                character: position.character
            }
        };

        if (selection && !selection.isEmpty) {
            const selectedText = document.getText(selection);
            currentFile.selection = {
                start: document.offsetAt(selection.start),
                end: document.offsetAt(selection.end),
                text: selectedText
            };
        }

        return currentFile;
    }

    private async getRelatedFiles(document: vscode.TextDocument): Promise<Array<{
        path: string;
        content: string;
        relevanceScore: number;
        reason: string;
    }>> {
        const relatedFiles: Array<{
            path: string;
            content: string;
            relevanceScore: number;
            reason: string;
        }> = [];

        try {
            // Get imports/requires from current file
            const imports = this.extractImports(document);
            
            // Find related files based on imports
            for (const importPath of imports) {
                const resolvedPath = await this.resolveImportPath(document.uri, importPath);
                if (resolvedPath) {
                    const relatedFile = await this.loadRelatedFile(resolvedPath, 'imported');
                    if (relatedFile) {
                        relatedFiles.push(relatedFile);
                    }
                }
            }

            // Find files in the same directory
            const sameDirectoryFiles = await this.getSameDirectoryFiles(document.uri);
            for (const file of sameDirectoryFiles) {
                const relatedFile = await this.loadRelatedFile(file, 'same-directory');
                if (relatedFile) {
                    relatedFiles.push(relatedFile);
                }
            }

            // Sort by relevance score
            relatedFiles.sort((a, b) => b.relevanceScore - a.relevanceScore);

            // Limit to top 5 most relevant files
            return relatedFiles.slice(0, 5);
        } catch (error) {
            console.error('Error gathering related files:', error);
            return [];
        }
    }

    private extractImports(document: vscode.TextDocument): string[] {
        const content = document.getText();
        const imports: string[] = [];
        const language = document.languageId;

        try {
            if (language === 'typescript' || language === 'javascript' || language === 'typescriptreact' || language === 'javascriptreact') {
                // Extract ES6 imports and CommonJS requires
                const importRegex = /(?:import.*?from\s+['"`]([^'"`]+)['"`]|require\s*\(\s*['"`]([^'"`]+)['"`]\s*\))/g;
                let match;
                while ((match = importRegex.exec(content)) !== null) {
                    const importPath = match[1] || match[2];
                    if (importPath && !importPath.startsWith('node_modules') && !importPath.startsWith('@')) {
                        imports.push(importPath);
                    }
                }
            } else if (language === 'python') {
                // Extract Python imports
                const importRegex = /(?:from\s+([^\s]+)\s+import|import\s+([^\s,]+))/g;
                let match;
                while ((match = importRegex.exec(content)) !== null) {
                    const importPath = match[1] || match[2];
                    if (importPath && !importPath.includes('.')) {
                        imports.push(importPath.replace('.', '/'));
                    }
                }
            }
        } catch (error) {
            console.error('Error extracting imports:', error);
        }

        return imports;
    }

    private async resolveImportPath(currentFileUri: vscode.Uri, importPath: string): Promise<vscode.Uri | null> {
        try {
            const workspaceFolder = vscode.workspace.getWorkspaceFolder(currentFileUri);
            if (!workspaceFolder) {
                return null;
            }

            const currentDir = path.dirname(currentFileUri.fsPath);
            
            // Handle relative imports
            if (importPath.startsWith('./') || importPath.startsWith('../')) {
                const resolvedPath = path.resolve(currentDir, importPath);
                const extensions = ['.ts', '.js', '.tsx', '.jsx', '.py'];
                
                for (const ext of extensions) {
                    const fullPath = resolvedPath + ext;
                    if (fs.existsSync(fullPath)) {
                        return vscode.Uri.file(fullPath);
                    }
                }
                
                // Check for index files
                const indexPath = path.join(resolvedPath, 'index');
                for (const ext of extensions) {
                    const fullPath = indexPath + ext;
                    if (fs.existsSync(fullPath)) {
                        return vscode.Uri.file(fullPath);
                    }
                }
            }

            return null;
        } catch (error) {
            console.error('Error resolving import path:', error);
            return null;
        }
    }

    private async getSameDirectoryFiles(currentFileUri: vscode.Uri): Promise<vscode.Uri[]> {
        try {
            const currentDir = path.dirname(currentFileUri.fsPath);
            const files = await vscode.workspace.fs.readDirectory(vscode.Uri.file(currentDir));
            
            return files
                .filter(([name, type]) => 
                    type === vscode.FileType.File && 
                    name !== path.basename(currentFileUri.fsPath) &&
                    this.SUPPORTED_EXTENSIONS.has(path.extname(name))
                )
                .map(([name]) => vscode.Uri.file(path.join(currentDir, name)))
                .slice(0, 3); // Limit to 3 files from same directory
        } catch (error) {
            console.error('Error getting same directory files:', error);
            return [];
        }
    }

    private async loadRelatedFile(uri: vscode.Uri, reason: string): Promise<{
        path: string;
        content: string;
        relevanceScore: number;
        reason: string;
    } | null> {
        try {
            const stat = await vscode.workspace.fs.stat(uri);
            if (stat.size > this.MAX_FILE_SIZE) {
                return null; // Skip large files
            }

            const content = await vscode.workspace.fs.readFile(uri);
            const relativePath = vscode.workspace.asRelativePath(uri);
            
            // Calculate relevance score
            let relevanceScore = 0.5;
            if (reason === 'imported') {
                relevanceScore = 0.9;
            } else if (reason === 'same-directory') {
                relevanceScore = 0.6;
            }

            return {
                path: relativePath,
                content: content.toString(),
                relevanceScore,
                reason
            };
        } catch (error) {
            console.error('Error loading related file:', error);
            return null;
        }
    }

    private async getProjectContext() {
        const projectContext: any = {};

        try {
            const workspaceFolders = vscode.workspace.workspaceFolders;
            if (!workspaceFolders || workspaceFolders.length === 0) {
                return projectContext;
            }

            const rootPath = workspaceFolders[0].uri.fsPath;

            // Load package.json if it exists
            const packageJsonPath = path.join(rootPath, 'package.json');
            if (fs.existsSync(packageJsonPath)) {
                const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
                projectContext.packageJson = {
                    name: packageJson.name,
                    dependencies: packageJson.dependencies,
                    devDependencies: packageJson.devDependencies,
                    scripts: packageJson.scripts
                };
            }

            // Load tsconfig.json if it exists
            const tsconfigPath = path.join(rootPath, 'tsconfig.json');
            if (fs.existsSync(tsconfigPath)) {
                const tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf8'));
                projectContext.tsConfig = tsconfig.compilerOptions;
            }

            // Get basic file structure
            projectContext.fileStructure = await this.getFileStructure(rootPath);

        } catch (error) {
            console.error('Error gathering project context:', error);
        }

        return projectContext;
    }

    private async getFileStructure(rootPath: string): Promise<string[]> {
        const structure: string[] = [];
        
        try {
            const files = await this.walkDirectory(rootPath, 2); // Max depth of 2
            return files.map(file => vscode.workspace.asRelativePath(file));
        } catch (error) {
            console.error('Error getting file structure:', error);
            return [];
        }
    }

    private async walkDirectory(dirPath: string, maxDepth: number, currentDepth: number = 0): Promise<string[]> {
        if (currentDepth >= maxDepth) {
            return [];
        }

        const files: string[] = [];
        
        try {
            const entries = fs.readdirSync(dirPath, { withFileTypes: true });
            
            for (const entry of entries) {
                if (entry.name.startsWith('.') || entry.name === 'node_modules') {
                    continue;
                }

                const fullPath = path.join(dirPath, entry.name);
                
                if (entry.isDirectory()) {
                    const subFiles = await this.walkDirectory(fullPath, maxDepth, currentDepth + 1);
                    files.push(...subFiles);
                } else if (this.SUPPORTED_EXTENSIONS.has(path.extname(entry.name))) {
                    files.push(fullPath);
                }
            }
        } catch (error) {
            console.error('Error walking directory:', error);
        }

        return files;
    }
}
