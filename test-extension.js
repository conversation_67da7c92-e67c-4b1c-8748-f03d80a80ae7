// Simple test script to verify VS Code extension functionality
// This can be run in the VS Code Developer Console

console.log('=== VS Code Extension Test ===');

// Test 1: Check if extension is loaded
console.log('1. Checking if extension is loaded...');
const extension = vscode.extensions.getExtension('ollama-code-assistant');
if (extension) {
    console.log('✅ Extension found:', extension.id);
    console.log('   Active:', extension.isActive);
} else {
    console.log('❌ Extension not found');
}

// Test 2: Check if commands are registered
console.log('2. Checking commands...');
vscode.commands.getCommands().then(commands => {
    const ollamaCommands = commands.filter(cmd => cmd.startsWith('ollama-assistant'));
    console.log('✅ Ollama commands found:', ollamaCommands);
});

// Test 3: Try to open chat
console.log('3. Testing chat opening...');
try {
    vscode.commands.executeCommand('ollama-assistant.openChat');
    console.log('✅ Chat command executed');
} catch (error) {
    console.log('❌ Chat command failed:', error);
}

// Test 4: Check configuration
console.log('4. Checking configuration...');
const config = vscode.workspace.getConfiguration('ollama-assistant');
console.log('   Server URL:', config.get('serverUrl'));
console.log('   Default Model:', config.get('defaultModel'));
console.log('   Chat Model:', config.get('chatModel'));

console.log('=== Test Complete ===');
