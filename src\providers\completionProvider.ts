import * as vscode from 'vscode';
import { OllamaClient } from '../services/ollamaClient';
import { ContextEngine } from '../services/contextEngine';
import { ConfigurationManager } from '../services/configurationManager';
import { CompletionRequest } from '../types/ollama';

export class CompletionProvider implements vscode.InlineCompletionItemProvider {
    private completionCache = new Map<string, { completion: string; timestamp: number }>();
    private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes
    private debounceTimer: NodeJS.Timeout | null = null;

    constructor(
        private ollamaClient: OllamaClient,
        private contextEngine: ContextEngine,
        private configManager: ConfigurationManager
    ) {}

    async provideInlineCompletionItems(
        document: vscode.TextDocument,
        position: vscode.Position,
        context: vscode.InlineCompletionContext,
        token: vscode.CancellationToken
    ): Promise<vscode.InlineCompletionItem[] | vscode.InlineCompletionList | null> {
        
        // Check if completions are enabled
        if (!this.configManager.isCompletionsEnabled()) {
            return null;
        }

        // Check if Ollama is connected
        if (!this.ollamaClient.isServerConnected()) {
            return null;
        }

        // Get the default model
        const model = this.configManager.getDefaultModel();
        if (!model) {
            return null;
        }

        try {
            // Debounce completion requests
            const delay = this.configManager.getCompletionDelay();
            if (this.debounceTimer) {
                clearTimeout(this.debounceTimer);
            }

            return new Promise((resolve) => {
                this.debounceTimer = setTimeout(async () => {
                    try {
                        const completion = await this.generateCompletion(document, position, model, token);
                        resolve(completion);
                    } catch (error) {
                        console.error('Completion generation error:', error);
                        resolve(null);
                    }
                }, delay);
            });

        } catch (error) {
            console.error('Completion provider error:', error);
            return null;
        }
    }

    private async generateCompletion(
        document: vscode.TextDocument,
        position: vscode.Position,
        model: string,
        token: vscode.CancellationToken
    ): Promise<vscode.InlineCompletionItem[] | null> {
        
        // Check cache first
        const cacheKey = this.getCacheKey(document, position);
        const cached = this.completionCache.get(cacheKey);
        if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
            return [new vscode.InlineCompletionItem(cached.completion)];
        }

        // Get context
        const context = await this.contextEngine.gatherContext(document, position);
        
        // Build prompt
        const prompt = this.buildCompletionPrompt(context, position);
        
        // Prepare completion request
        const request: CompletionRequest = {
            model,
            prompt,
            options: this.configManager.getCompletionOptions(),
            stream: false
        };

        try {
            const response = await this.ollamaClient.completion(request);
            
            if (token.isCancellationRequested) {
                return null;
            }

            const completion = this.processCompletionResponse(response.response);
            
            if (completion) {
                // Cache the result
                this.completionCache.set(cacheKey, {
                    completion,
                    timestamp: Date.now()
                });

                return [new vscode.InlineCompletionItem(completion)];
            }

            return null;
        } catch (error) {
            console.error('Error generating completion:', error);
            return null;
        }
    }

    private buildCompletionPrompt(context: any, position: vscode.Position): string {
        let prompt = '';

        // Add file context
        if (context.currentFile) {
            const { content, language, cursorPosition } = context.currentFile;
            const lines = content.split('\n');
            
            // Get context around cursor
            const startLine = Math.max(0, cursorPosition.line - 10);
            const endLine = Math.min(lines.length, cursorPosition.line + 5);
            const contextLines = lines.slice(startLine, endLine);
            
            prompt += `Language: ${language}\n\n`;
            prompt += `Code context:\n`;
            prompt += '```' + language + '\n';
            
            contextLines.forEach((line: string, index: number) => {
                const lineNumber = startLine + index;
                const marker = lineNumber === cursorPosition.line ? ' <-- CURSOR' : '';
                prompt += `${line}${marker}\n`;
            });
            
            prompt += '```\n\n';
        }

        // Add related files context (abbreviated)
        if (context.relatedFiles && context.relatedFiles.length > 0) {
            prompt += 'Related files:\n';
            context.relatedFiles.slice(0, 2).forEach((file: any) => {
                prompt += `- ${file.path} (${file.reason})\n`;
            });
            prompt += '\n';
        }

        prompt += 'Complete the code at the cursor position. Provide only the completion text without any explanations or markdown formatting:';

        return prompt;
    }

    private processCompletionResponse(response: string): string | null {
        if (!response || response.trim().length === 0) {
            return null;
        }

        // Clean up the response
        let completion = response.trim();
        
        // Remove common prefixes that might be added by the model
        const prefixesToRemove = [
            'Here is the completion:',
            'The completion is:',
            'Complete:',
            'Completion:',
            '```',
            'Here\'s the code:',
            'The code should be:'
        ];

        for (const prefix of prefixesToRemove) {
            if (completion.toLowerCase().startsWith(prefix.toLowerCase())) {
                completion = completion.substring(prefix.length).trim();
            }
        }

        // Remove trailing markdown or explanations
        const lines = completion.split('\n');
        const codeLines: string[] = [];
        
        for (const line of lines) {
            // Stop at explanatory text or markdown
            if (line.trim().startsWith('//') && line.includes('explanation')) {
                break;
            }
            if (line.trim().startsWith('```')) {
                break;
            }
            if (line.toLowerCase().includes('this code') || line.toLowerCase().includes('explanation')) {
                break;
            }
            codeLines.push(line);
        }

        completion = codeLines.join('\n').trim();

        // Limit completion length
        if (completion.length > 500) {
            completion = completion.substring(0, 500);
            // Try to end at a complete line
            const lastNewline = completion.lastIndexOf('\n');
            if (lastNewline > 200) {
                completion = completion.substring(0, lastNewline);
            }
        }

        return completion.length > 0 ? completion : null;
    }

    private getCacheKey(document: vscode.TextDocument, position: vscode.Position): string {
        const content = document.getText();
        const prefix = content.substring(0, document.offsetAt(position));
        // Use last 100 characters as cache key to avoid huge keys
        const shortPrefix = prefix.length > 100 ? prefix.substring(prefix.length - 100) : prefix;
        return `${document.uri.toString()}:${shortPrefix}`;
    }

    // Clean up old cache entries
    private cleanupCache(): void {
        const now = Date.now();
        for (const [key, value] of this.completionCache.entries()) {
            if (now - value.timestamp > this.CACHE_TTL) {
                this.completionCache.delete(key);
            }
        }
    }

    dispose(): void {
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }
        this.completionCache.clear();
    }
}
