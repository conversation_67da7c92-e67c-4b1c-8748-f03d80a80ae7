import * as vscode from 'vscode';
import { OllamaClient } from '../services/ollamaClient';
import { AugmentAgent } from '../core/augmentAgent';
import { AdvancedContextEngine } from '../core/contextEngine';
import { MemorySystem } from '../core/memorySystem';
import { CodeCheckpoints } from '../core/codeCheckpoints';
import { ChatMessage } from '../types/ollama';

export class ModernChatProvider {
    private chatPanel: vscode.WebviewPanel | undefined;
    private conversationHistory: ChatMessage[] = [];
    private augmentAgent: AugmentAgent;
    private contextEngine: AdvancedContextEngine;
    private memorySystem: MemorySystem;
    private checkpoints: CodeCheckpoints;
    private currentMode: 'chat' | 'agent' | 'agent-auto' = 'agent';
    private isStreaming = false;
    private sessionId: string;
    private streamingAbortController: AbortController | undefined;

    constructor(
        private context: vscode.ExtensionContext,
        private ollamaClient: OllamaClient
    ) {
        this.contextEngine = new AdvancedContextEngine();
        this.memorySystem = new MemorySystem(context);
        this.checkpoints = new CodeCheckpoints(context);
        this.augmentAgent = new AugmentAgent(ollamaClient, context);
        this.sessionId = this.generateSessionId();
        this.loadPersistedSession();
    }

    async openChat(): Promise<void> {
        console.log('ModernChatProvider.openChat() called');

        if (this.chatPanel) {
            console.log('Chat panel already exists, revealing it');
            this.chatPanel.reveal(vscode.ViewColumn.One);
            return;
        }

        // Mark this session as active
        this.markSessionActive();

        console.log('Creating new chat panel');
        this.chatPanel = vscode.window.createWebviewPanel(
            'augment-chat',
            '🤖 Augment Assistant',
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [
                    vscode.Uri.joinPath(this.context.extensionUri, 'media'),
                    vscode.Uri.joinPath(this.context.extensionUri, 'out')
                ]
            }
        );
        console.log('Chat panel created successfully');

        this.chatPanel.webview.html = this.getWebviewContent();

        // Load models automatically when chat panel opens
        setTimeout(async () => {
            await this.handleGetModels();

            // Send initial mode and streaming status to webview
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'modeChanged',
                    mode: this.currentMode
                });
                this.chatPanel.webview.postMessage({
                    type: 'setStreamingStatus',
                    status: this.isStreaming
                });
            }
        }, 200);

        // Handle messages from webview
        this.chatPanel.webview.onDidReceiveMessage(
            async (message) => {
                switch (message.type) {
                    case 'sendMessage':
                        await this.handleSendMessage(message.text, message.includeContext);
                        break;
                    case 'sendStreamingMessage':
                        await this.handleStreamingMessage(message.text, message.includeContext);
                        break;
                    case 'clearHistory':
                        await this.handleClearHistory();
                        break;
                    case 'getModels':
                        await this.handleGetModels();
                        break;
                    case 'selectModel':
                        await this.handleSelectModel(message.model);
                        break;
                    case 'applyCode':
                        await this.handleApplyCode(message.code, message.language);
                        break;
                    case 'createFile':
                        await this.handleCreateFile(message.filename, message.content, message.language);
                        break;
                    case 'copyCode':
                        await this.handleCopyCode(message.code);
                        break;
                    case 'openFile':
                        await this.handleOpenFile(message.filename);
                        break;
                    case 'deleteFile':
                        await this.handleDeleteFile(message.filename);
                        break;
                    case 'executeAction':
                        await this.handleExecuteAction(message.action);
                        break;
                    case 'fileOperation':
                        await this.handleFileOperation(message.operation, message.filename, message.content);
                        break;
                    case 'setMode':
                        await this.handleSetMode(message.mode);
                        break;
                    case 'createCheckpoint':
                        await this.handleCreateCheckpoint(message.description);
                        break;
                    case 'restoreCheckpoint':
                        await this.handleRestoreCheckpoint(message.checkpointId);
                        break;
                    case 'getCheckpoints':
                        await this.handleGetCheckpoints();
                        break;
                    case 'testStreaming':
                        await this.handleTestStreaming();
                        break;
                    case 'switchMode':
                        await this.handleSwitchMode(message.mode);
                        break;
                    case 'runTerminalCommand':
                        await this.handleRunTerminalCommand(message.command);
                        break;
                }
            },
            undefined,
            this.context.subscriptions
        );

        this.chatPanel.onDidDispose(() => {
            this.chatPanel = undefined;
            // Clear active session when panel is disposed
            this.clearActiveSession();
        });
    }

    private async handleGetModels(): Promise<void> {
        try {
            console.log('ModernChatProvider: Getting models...');
            await this.ollamaClient.refreshModels();
            const models = this.ollamaClient.getAvailableModels();
            console.log('ModernChatProvider: Found models:', models.map(m => m.name));
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'availableModels',
                    models: models.map(m => m.name)
                });
                console.log('ModernChatProvider: Sent models to webview');
            }
        } catch (error) {
            console.error('Error getting models:', error);
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'error',
                    message: 'Failed to load models'
                });
            }
        }
    }

    private async handleSelectModel(model: string): Promise<void> {
        try {
            await vscode.workspace.getConfiguration('ollama-assistant').update('chatModel', model, vscode.ConfigurationTarget.Global);
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'modelSelected',
                    model
                });
            }
        } catch (error) {
            console.error('Error selecting model:', error);
        }
    }

    private async handleSendMessage(text: string, includeContext: boolean): Promise<void> {
        if (!text.trim()) return;
        if (this.isStreaming) return;

        this.isStreaming = true;

        const userMessage: ChatMessage = {
            role: 'user',
            content: text
        };

        this.conversationHistory.push(userMessage);

        if (this.chatPanel) {
            this.chatPanel.webview.postMessage({
                type: 'userMessage',
                message: text
            });
        }

        try {
            // Get current editor context
            const activeEditor = vscode.window.activeTextEditor;
            const selection = activeEditor?.selection;

            // Determine which processing mode to use based on current mode
            if (this.currentMode === 'chat') {
                await this.processChatMode(text, includeContext, activeEditor, selection);
            } else if (this.currentMode === 'agent') {
                await this.processAgentMode(text, includeContext, activeEditor, selection);
            } else if (this.currentMode === 'agent-auto') {
                await this.processAgentAutoMode(text, includeContext, activeEditor, selection);
            }

        } catch (error) {
            console.error('Error in message processing:', error);
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'error',
                    message: `Error: ${error}`
                });
                // Send streaming error to reset UI state
                this.chatPanel.webview.postMessage({
                    type: 'streamingError',
                    message: `Error: ${error}`
                });
            }
        } finally {
            this.isStreaming = false;
            this.streamingAbortController = undefined;
            // Ensure webview knows streaming has stopped
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'setStreamingStatus',
                    status: false
                });
            }
        }
    }

    /**
     * Process message in chat mode - natural conversation with mode switching suggestions
     */
    private async processChatMode(text: string, includeContext: boolean, activeEditor?: vscode.TextEditor, selection?: vscode.Selection): Promise<void> {
        if (this.chatPanel) {
            this.chatPanel.webview.postMessage({
                type: 'assistantMessageStart'
            });
        }

        // Use the dedicated chat processing method
        const chatResponse = await this.augmentAgent.processChatRequest(text, includeContext, activeEditor?.document, selection);

        // Stream the chat response content
        if (this.chatPanel) {
            this.chatPanel.webview.postMessage({
                type: 'assistantMessageChunk',
                content: chatResponse.content
            });

            // Check if AI suggests switching to agent mode
            if (chatResponse.suggestAgentMode) {
                this.chatPanel.webview.postMessage({
                    type: 'suggestModeSwitch',
                    mode: 'agent',
                    reason: chatResponse.agentModeReason || 'This looks like a coding task that would benefit from Agent mode.'
                });
            }

            this.chatPanel.webview.postMessage({
                type: 'assistantMessageEnd'
            });
        }

        // Add to conversation history
        const assistantMessage: ChatMessage = {
            role: 'assistant',
            content: chatResponse.content
        };
        this.conversationHistory.push(assistantMessage);

        // Store in memory for persistence
        await this.storeConversationInMemory(text, chatResponse.content, 'chat');
    }

    /**
     * Process message in agent mode - streaming command execution
     */
    private async processAgentMode(text: string, includeContext: boolean, activeEditor?: vscode.TextEditor, selection?: vscode.Selection): Promise<void> {
        if (this.chatPanel) {
            this.chatPanel.webview.postMessage({
                type: 'streamingStart',
                message: 'Agent is analyzing the request and formulating a plan...'
            });
        }

        // Create abort controller for this streaming session
        this.streamingAbortController = new AbortController();

        // Use the streaming agent system with real-time command execution
        await this.augmentAgent.processRequestStream(
            text,
            includeContext,
            activeEditor?.document,
            selection,
            async (command) => {
                // Check if streaming was aborted
                if (this.streamingAbortController?.signal.aborted) {
                    throw new Error('Streaming aborted by user');
                }

                // Store command in memory for learning
                await this.storeCommandInMemory(text, command);

                // Send command to UI for real-time display
                if (this.chatPanel) {
                    this.chatPanel.webview.postMessage({
                        type: 'streamingCommand',
                        command: command
                    });
                }

                // Small delay to make the streaming visible
                await new Promise(resolve => setTimeout(resolve, 500));
            },
            'agent'
        );

        // Send completion message
        if (this.chatPanel) {
            this.chatPanel.webview.postMessage({
                type: 'streamingComplete',
                message: 'All tasks completed successfully!'
            });
        }

        // Add to conversation history
        const assistantMessage: ChatMessage = {
            role: 'assistant',
            content: `Completed agent task: ${text}`
        };
        this.conversationHistory.push(assistantMessage);

        // Store in memory for persistence
        await this.storeConversationInMemory(text, assistantMessage.content, 'agent');
    }

    /**
     * Process message in agent-auto mode - streaming command execution without user confirmation
     */
    private async processAgentAutoMode(text: string, includeContext: boolean, activeEditor?: vscode.TextEditor, selection?: vscode.Selection): Promise<void> {
        if (this.chatPanel) {
            this.chatPanel.webview.postMessage({
                type: 'streamingStart',
                message: 'Agent Auto Mode: Analyzing and executing automatically...'
            });
        }

        // Create abort controller for this streaming session
        this.streamingAbortController = new AbortController();

        // Use the streaming agent system with automatic execution (no delays)
        await this.augmentAgent.processRequestStream(
            text,
            includeContext,
            activeEditor?.document,
            selection,
            async (command) => {
                // Check if streaming was aborted
                if (this.streamingAbortController?.signal.aborted) {
                    throw new Error('Streaming aborted by user');
                }

                // Store command in memory for learning
                await this.storeCommandInMemory(text, command);

                // Send command to UI for real-time display
                if (this.chatPanel) {
                    this.chatPanel.webview.postMessage({
                        type: 'streamingCommand',
                        command: command
                    });
                }

                // No delay in auto mode - execute immediately
            },
            'agent-auto'
        );

        // Send completion message
        if (this.chatPanel) {
            this.chatPanel.webview.postMessage({
                type: 'streamingComplete',
                message: 'Auto execution completed successfully!'
            });
        }

        // Add to conversation history
        const assistantMessage: ChatMessage = {
            role: 'assistant',
            content: `Completed auto agent task: ${text}`
        };
        this.conversationHistory.push(assistantMessage);

        // Store in memory for persistence
        await this.storeConversationInMemory(text, assistantMessage.content, 'agent-auto');
    }

    /**
     * NEW: Handle streaming message with command pipeline
     */
    private async handleStreamingMessage(text: string, includeContext: boolean): Promise<void> {
        if (!text.trim()) return;
        if (this.isStreaming) return;

        this.isStreaming = true;

        const userMessage: ChatMessage = {
            role: 'user',
            content: text
        };

        this.conversationHistory.push(userMessage);

        if (this.chatPanel) {
            // Send user message to UI
            this.chatPanel.webview.postMessage({
                type: 'userMessage',
                message: text
            });

            // Start streaming indicator
            this.chatPanel.webview.postMessage({
                type: 'streamingStart',
                message: 'Agent is analyzing the request...'
            });
        }

        try {
            // Create abort controller for this streaming session
            this.streamingAbortController = new AbortController();

            // Get current editor context
            const activeEditor = vscode.window.activeTextEditor;
            const selection = activeEditor?.selection;

            // Use the new streaming agent system with error handling
            await this.augmentAgent.processRequestStream(
                text,
                includeContext,
                activeEditor?.document,
                selection,
                async (command) => {
                    // Check if streaming was aborted
                    if (this.streamingAbortController?.signal.aborted) {
                        throw new Error('Streaming aborted by user');
                    }

                    // This callback is called for each command as it's processed
                    if (this.chatPanel) {
                        this.chatPanel.webview.postMessage({
                            type: 'streamingCommand',
                            command: command
                        });
                    }
                },
                'agent'
            );

            // Send completion message
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'streamingComplete',
                    message: 'All tasks completed successfully!'
                });
            }

            // Add to conversation history
            const assistantMessage: ChatMessage = {
                role: 'assistant',
                content: `Completed streaming task: ${text}`
            };
            this.conversationHistory.push(assistantMessage);

        } catch (error) {
            console.error('Error in streaming agent processing:', error);
            await this.handleStreamingInterruption(error);
            // Send streaming error to reset UI state
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'streamingError',
                    message: `Error: ${error}`
                });
            }
        } finally {
            this.isStreaming = false;
            this.streamingAbortController = undefined;
            // Ensure webview knows streaming has stopped
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'setStreamingStatus',
                    status: false
                });
            }
        }
    }

    /**
     * Test streaming functionality with a simple example
     */
    private async handleTestStreaming(): Promise<void> {
        if (this.isStreaming) return;

        // Simulate a streaming request
        await this.handleSendMessage("Create a simple React component called HelloWorld", true);
    }

    /**
     * Handle mode switching from UI
     */
    private async handleSwitchMode(mode: 'chat' | 'agent' | 'agent-auto'): Promise<void> {
        this.currentMode = mode;
        if (this.chatPanel) {
            this.chatPanel.webview.postMessage({
                type: 'modeChanged',
                mode: this.currentMode
            });
        }
        console.log(`Switched to ${mode} mode`);
    }

    /**
     * Handle terminal command execution from UI
     */
    private async handleRunTerminalCommand(command: string): Promise<void> {
        try {
            // Create a new terminal for the command
            const terminal = vscode.window.createTerminal({
                name: 'Augment Assistant',
                cwd: vscode.workspace.workspaceFolders?.[0]?.uri.fsPath
            });

            // Show the terminal and run the command
            terminal.show();
            terminal.sendText(command);

            console.log('Executed terminal command:', command);

            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'commandExecuted',
                    command: command,
                    success: true
                });
            }
        } catch (error) {
            console.error('Error executing terminal command:', error);

            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'commandExecuted',
                    command: command,
                    success: false,
                    error: error
                });
            }
        }
    }

    /**
     * Store command execution in memory for learning and context
     */
    private async storeCommandInMemory(userRequest: string, command: any): Promise<void> {
        try {
            // Create an AgentMemory entry for this command execution
            const memory = {
                id: `cmd_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
                timestamp: new Date(),
                context: `User: ${userRequest} | Action: ${command.action} | File: ${command.file_path || 'N/A'}`,
                codeStyle: {
                    language: this.detectLanguageFromPath(command.file_path),
                    patterns: this.extractPatternsFromCommand(command)
                },
                patterns: [command.action, command.task_description],
                preferences: {
                    action_type: command.action,
                    file_type: command.file_path ? command.file_path.split('.').pop() : null
                }
            };

            // Store in memory system for future reference
            await this.memorySystem.addMemory(memory);

        } catch (error) {
            console.error('Error storing command in memory:', error);
            // Don't throw - memory storage shouldn't break the main flow
        }
    }

    /**
     * Detect programming language from file path
     */
    private detectLanguageFromPath(filePath?: string): string {
        if (!filePath) return 'unknown';

        const extension = filePath.split('.').pop()?.toLowerCase();
        const languageMap: { [key: string]: string } = {
            'ts': 'typescript',
            'tsx': 'typescript',
            'js': 'javascript',
            'jsx': 'javascript',
            'py': 'python',
            'java': 'java',
            'cpp': 'cpp',
            'c': 'c',
            'cs': 'csharp',
            'go': 'go',
            'rs': 'rust',
            'php': 'php',
            'rb': 'ruby',
            'html': 'html',
            'css': 'css',
            'scss': 'scss',
            'json': 'json',
            'md': 'markdown'
        };

        return languageMap[extension || ''] || 'unknown';
    }

    /**
     * Extract patterns from command for learning
     */
    private extractPatternsFromCommand(command: any): string[] {
        const patterns: string[] = [];

        if (command.action) {
            patterns.push(`action_${command.action}`);
        }

        if (command.file_path) {
            const extension = command.file_path.split('.').pop();
            if (extension) {
                patterns.push(`file_${extension}`);
            }

            // Detect component patterns
            if (command.file_path.includes('component') || command.file_path.includes('Component')) {
                patterns.push('component_creation');
            }

            if (command.file_path.includes('test') || command.file_path.includes('spec')) {
                patterns.push('test_file');
            }
        }

        if (command.content) {
            // Detect React patterns
            if (command.content.includes('React') || command.content.includes('useState') || command.content.includes('useEffect')) {
                patterns.push('react_development');
            }

            // Detect API patterns
            if (command.content.includes('express') || command.content.includes('app.get') || command.content.includes('app.post')) {
                patterns.push('api_development');
            }

            // Detect TypeScript patterns
            if (command.content.includes('interface') || command.content.includes(': string') || command.content.includes(': number')) {
                patterns.push('typescript_usage');
            }
        }

        return patterns;
    }

    /**
     * Store conversation in memory for persistence across sessions
     */
    private async storeConversationInMemory(userMessage: string, assistantResponse: string, mode: string): Promise<void> {
        try {
            // Create a conversation memory entry
            const memory = {
                id: `conv_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
                timestamp: new Date(),
                context: `Mode: ${mode} | User: ${userMessage.substring(0, 100)}... | Assistant: ${assistantResponse.substring(0, 100)}...`,
                codeStyle: {
                    mode: mode,
                    language: 'general',
                    patterns: this.extractConversationPatterns(userMessage, assistantResponse)
                },
                patterns: [mode, 'conversation'],
                preferences: {
                    interaction_mode: mode,
                    response_type: mode === 'chat' ? 'conversational' : 'action_based'
                }
            };

            // Store in memory system
            await this.memorySystem.addMemory(memory);

        } catch (error) {
            console.error('Error storing conversation in memory:', error);
            // Don't throw - memory storage shouldn't break the main flow
        }
    }

    /**
     * Extract patterns from conversation for learning
     */
    private extractConversationPatterns(userMessage: string, assistantResponse: string): string[] {
        const patterns: string[] = [];

        const lowerUser = userMessage.toLowerCase();
        const lowerAssistant = assistantResponse.toLowerCase();

        // Question patterns
        if (lowerUser.includes('how') || lowerUser.includes('what') || lowerUser.includes('why')) {
            patterns.push('question_asking');
        }

        // Help patterns
        if (lowerUser.includes('help') || lowerUser.includes('explain') || lowerUser.includes('understand')) {
            patterns.push('help_seeking');
        }

        // Technology patterns
        if (lowerUser.includes('react') || lowerAssistant.includes('react')) {
            patterns.push('react_discussion');
        }

        if (lowerUser.includes('typescript') || lowerAssistant.includes('typescript')) {
            patterns.push('typescript_discussion');
        }

        if (lowerUser.includes('api') || lowerAssistant.includes('api')) {
            patterns.push('api_discussion');
        }

        // Mode switching patterns
        if (lowerAssistant.includes('agent mode') || lowerAssistant.includes('switch to')) {
            patterns.push('mode_switching_suggested');
        }

        return patterns;
    }

    private buildSystemPrompt(contextBundle?: any): string {
        let prompt = `You are Augment Assistant, an expert AI coding assistant with autonomous file operations.

CRITICAL INSTRUCTIONS FOR FILE OPERATIONS:
🚨 ALWAYS CREATE ACTUAL FILES - DON'T JUST SHOW CODE! 🚨

When the user asks you to create, make, or generate a file:
1. IMMEDIATELY use the FILE_OPERATION system
2. Put the ACTUAL content inside the operation
3. The file will be created automatically in their workspace

MANDATORY FILE OPERATION FORMAT:
FILE_OPERATION:CREATE:filename.ext:
[PUT THE ACTUAL FILE CONTENT HERE - NOT JUST COMMENTS]
END_FILE_OPERATION

CORRECT EXAMPLE:
User: "create a simple hello.js file"
Response: I'll create a hello.js file with actual working code.

FILE_OPERATION:CREATE:hello.js:
console.log("Hello, World!");
console.log("This is a working JavaScript file!");

function sayHello(name) {
    return "Hello, " + name + "!";
}

console.log(sayHello("Developer"));
END_FILE_OPERATION

✅ Your hello.js file has been created and is ready to run!

WRONG EXAMPLE (DON'T DO THIS):
FILE_OPERATION:CREATE:hello.js:
// Your code here
END_FILE_OPERATION

🚨 ALWAYS PUT REAL, WORKING CODE INSIDE FILE_OPERATION BLOCKS! 🚨

CONTEXT INFORMATION:`;

        if (contextBundle?.currentFile) {
            prompt += `\n\n**Current File Context:**`;
            prompt += `\n- File: ${contextBundle.currentFile.path}`;
            prompt += `\n- Language: ${contextBundle.currentFile.language}`;
            if (contextBundle.currentFile.selection) {
                prompt += `\n- Selected Code:\n\`\`\`${contextBundle.currentFile.language}\n${contextBundle.currentFile.selection.text}\n\`\`\``;
            }
        }

        if (contextBundle?.projectContext) {
            prompt += `\n\n**Project Context:**`;
            if (contextBundle.projectContext.packageJson) {
                prompt += `\n- Package.json dependencies available`;
            }
            if (contextBundle.projectContext.fileStructure) {
                prompt += `\n- Project structure: ${contextBundle.projectContext.fileStructure.slice(0, 10).join(', ')}${contextBundle.projectContext.fileStructure.length > 10 ? '...' : ''}`;
            }
        }

        prompt += `\n\nRemember: Always use proper markdown formatting and provide actionable, well-structured responses!`;

        return prompt;
    }

    private async executeAgentActions(actions: any[]): Promise<void> {
        for (const action of actions) {
            try {
                await this.executeAgentAction(action);
            } catch (error) {
                console.error('Failed to execute agent action:', error);
                vscode.window.showErrorMessage(`Failed to execute action: ${error}`);
            }
        }
    }

    private async executeAgentAction(action: any): Promise<void> {
        switch (action.type) {
            case 'create_file':
                await this.executeCreateFileAction(action);
                break;
            case 'modify_file':
                await this.executeModifyFileAction(action);
                break;
            case 'delete_file':
                await this.executeDeleteFileAction(action);
                break;
            case 'run_command':
                await this.executeRunCommandAction(action);
                break;
            default:
                console.log('Unknown action type:', action.type);
        }
    }

    private async executeCreateFileAction(action: any): Promise<void> {
        // SECURITY NOTE: ALWAYS ASK FOR USER CONFIRMATION!
        const confirmed = await vscode.window.showInformationMessage(
            `Augment Agent wants to create file: ${action.target}. Proceed?`,
            'Yes', 'No'
        );

        if (confirmed === 'Yes') {
            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            if (!workspaceFolder) {
                vscode.window.showErrorMessage('No workspace folder open');
                return;
            }

            // Handle both relative and absolute paths
            let fileUri: vscode.Uri;
            const filePath = action.target;

            // Check if it's an absolute path (Windows or Unix)
            if (filePath.match(/^[a-zA-Z]:\\/) || filePath.startsWith('/')) {
                // Absolute path - use as is
                fileUri = vscode.Uri.file(filePath);
            } else {
                // Relative path - join with workspace
                fileUri = vscode.Uri.joinPath(workspaceFolder.uri, filePath);
            }

            console.log('Creating file at:', fileUri.fsPath);

            const encoder = new TextEncoder();
            const content = encoder.encode(action.content || '');

            // Ensure directory exists
            const dirUri = vscode.Uri.joinPath(fileUri, '..');
            try {
                await vscode.workspace.fs.createDirectory(dirUri);
            } catch (error) {
                // Directory might already exist, that's fine
                console.log('Directory creation note:', error);
            }

            await vscode.workspace.fs.writeFile(fileUri, content);
            await vscode.window.showTextDocument(fileUri);

            vscode.window.showInformationMessage(`✅ Created file: ${filePath}`);

            // Send notification to webview
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'fileOperationExecuted',
                    operation: 'CREATE',
                    filename: filePath,
                    success: true
                });
            }
        }
    }

    private async executeModifyFileAction(action: any): Promise<void> {
        // SECURITY: Always ask for user confirmation
        const confirmed = await vscode.window.showInformationMessage(
            `Augment Agent wants to modify file: ${action.target}. Proceed?`,
            'Yes', 'No'
        );

        if (confirmed === 'Yes') {
            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            if (!workspaceFolder) {
                vscode.window.showErrorMessage('No workspace folder open');
                return;
            }

            // Handle both relative and absolute paths
            let fileUri: vscode.Uri;
            const filePath = action.target;

            // Check if it's an absolute path (Windows or Unix)
            if (filePath.match(/^[a-zA-Z]:\\/) || filePath.startsWith('/')) {
                // Absolute path - use as is
                fileUri = vscode.Uri.file(filePath);
            } else {
                // Relative path - join with workspace
                fileUri = vscode.Uri.joinPath(workspaceFolder.uri, filePath);
            }

            console.log('Modifying file at:', fileUri.fsPath);

            const encoder = new TextEncoder();
            const content = encoder.encode(action.content || '');

            await vscode.workspace.fs.writeFile(fileUri, content);

            // Open the modified file
            const document = await vscode.workspace.openTextDocument(fileUri);
            await vscode.window.showTextDocument(document);

            vscode.window.showInformationMessage(`✅ Modified file: ${filePath}`);

            // Send notification to webview
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'fileOperationExecuted',
                    operation: 'MODIFY',
                    filename: filePath,
                    success: true
                });
            }
        }
    }

    private async executeDeleteFileAction(action: any): Promise<void> {
        // SECURITY: Always ask for user confirmation
        const confirmed = await vscode.window.showWarningMessage(
            `Augment Agent wants to delete file: ${action.target}. This cannot be undone. Proceed?`,
            'Yes', 'No'
        );

        if (confirmed === 'Yes') {
            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            if (!workspaceFolder) {
                vscode.window.showErrorMessage('No workspace folder open');
                return;
            }

            const fileUri = vscode.Uri.joinPath(workspaceFolder.uri, action.target);

            await vscode.workspace.fs.delete(fileUri);

            vscode.window.showInformationMessage(`✅ Deleted file: ${action.target}`);

            // Send notification to webview
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'fileOperationExecuted',
                    operation: 'DELETE',
                    filename: action.target,
                    success: true
                });
            }
        }
    }

    private async executeRunCommandAction(action: any): Promise<void> {
        // SECURITY: Always ask for user confirmation for commands
        const confirmed = await vscode.window.showInformationMessage(
            `Augment Agent wants to run command: ${action.target}. Proceed?`,
            'Yes', 'No'
        );

        if (confirmed === 'Yes') {
            const terminal = vscode.window.createTerminal('Augment Agent');
            terminal.show();
            terminal.sendText(action.target);

            vscode.window.showInformationMessage(`✅ Running command: ${action.target}`);

            // Send notification to webview
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'fileOperationExecuted',
                    operation: 'COMMAND',
                    filename: action.target,
                    success: true
                });
            }
        }
    }

    private async handleClearHistory(): Promise<void> {
        this.conversationHistory = [];
        if (this.chatPanel) {
            this.chatPanel.webview.postMessage({
                type: 'historyCleared'
            });
        }
    }

    private async handleApplyCode(code: string, language: string): Promise<void> {
        const activeEditor = vscode.window.activeTextEditor;
        if (!activeEditor) {
            vscode.window.showErrorMessage('No active editor to apply code to');
            return;
        }

        const selection = activeEditor.selection;
        await activeEditor.edit(editBuilder => {
            if (selection.isEmpty) {
                editBuilder.insert(selection.start, code);
            } else {
                editBuilder.replace(selection, code);
            }
        });

        vscode.window.showInformationMessage('Code applied successfully!');
    }

    private async handleCreateFile(filename: string, content: string, language: string): Promise<void> {
        try {
            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            if (!workspaceFolder) {
                vscode.window.showErrorMessage('No workspace folder open');
                return;
            }

            // Auto-detect filename if not provided or if language is 'auto'
            let finalFilename = filename;
            if (language === 'auto' || !filename.includes('.')) {
                const detectedLang = this.detectLanguageFromContent(content);
                const extension = this.getExtensionForLanguage(detectedLang);

                if (!filename.includes('.')) {
                    finalFilename = filename + extension;
                }
            }

            const filePath = vscode.Uri.joinPath(workspaceFolder.uri, finalFilename);

            // Check if file already exists
            try {
                await vscode.workspace.fs.stat(filePath);
                const choice = await vscode.window.showWarningMessage(
                    `File ${finalFilename} already exists. What would you like to do?`,
                    'Overwrite',
                    'Create with different name',
                    'Cancel'
                );

                if (choice === 'Cancel') {
                    return;
                } else if (choice === 'Create with different name') {
                    const newName = await vscode.window.showInputBox({
                        prompt: 'Enter a new filename',
                        value: finalFilename
                    });
                    if (!newName) return;
                    finalFilename = newName;
                }
            } catch (error) {
                // File doesn't exist, which is fine
            }

            const finalPath = vscode.Uri.joinPath(workspaceFolder.uri, finalFilename);
            await vscode.workspace.fs.writeFile(finalPath, Buffer.from(content, 'utf8'));

            const document = await vscode.workspace.openTextDocument(finalPath);
            await vscode.window.showTextDocument(document);

            vscode.window.showInformationMessage(`✅ File ${finalFilename} created successfully!`);
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to create file: ${error}`);
        }
    }

    private detectLanguageFromContent(content: string): string {
        // Simple language detection based on content patterns
        if (content.includes('import ') && content.includes('def ')) return 'python';
        if (content.includes('function ') || content.includes('const ') || content.includes('let ')) return 'javascript';
        if (content.includes('interface ') || content.includes(': string') || content.includes(': number')) return 'typescript';
        if (content.includes('public class ') || content.includes('import java.')) return 'java';
        if (content.includes('#include') || content.includes('int main(')) return 'cpp';
        if (content.includes('<html') || content.includes('<!DOCTYPE')) return 'html';
        if (content.includes('body {') || content.includes('.class')) return 'css';
        if (content.includes('{') && content.includes('"')) return 'json';
        return 'text';
    }

    private getExtensionForLanguage(language: string): string {
        const extensions: { [key: string]: string } = {
            'javascript': '.js',
            'typescript': '.ts',
            'python': '.py',
            'java': '.java',
            'cpp': '.cpp',
            'c': '.c',
            'html': '.html',
            'css': '.css',
            'json': '.json',
            'markdown': '.md',
            'yaml': '.yml',
            'xml': '.xml',
            'text': '.txt'
        };
        return extensions[language] || '.txt';
    }

    private async handleExecuteAction(action: any): Promise<void> {
        try {
            const result = await this.augmentAgent.executeActions([action]);
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'actionExecuted',
                    action,
                    result
                });
            }
            vscode.window.showInformationMessage(`Action executed: ${action.description}`);
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to execute action: ${error}`);
        }
    }

    private async handleSetMode(mode: 'chat' | 'agent' | 'agent-auto'): Promise<void> {
        this.currentMode = mode;
        if (this.chatPanel) {
            this.chatPanel.webview.postMessage({
                type: 'modeChanged',
                mode
            });
        }
        vscode.window.showInformationMessage(`Mode changed to: ${mode}`);
    }

    private async handleCreateCheckpoint(description: string): Promise<void> {
        try {
            const checkpointId = await this.checkpoints.create(description);
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'checkpointCreated',
                    checkpointId,
                    description
                });
            }
            vscode.window.showInformationMessage(`Checkpoint created: ${description}`);
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to create checkpoint: ${error}`);
        }
    }

    private async handleRestoreCheckpoint(checkpointId: string): Promise<void> {
        try {
            await this.checkpoints.restore(checkpointId);
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'checkpointRestored',
                    checkpointId
                });
            }
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to restore checkpoint: ${error}`);
        }
    }

    private async handleGetCheckpoints(): Promise<void> {
        try {
            const checkpoints = this.checkpoints.getCheckpoints();
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'checkpointsList',
                    checkpoints: checkpoints.map(cp => ({
                        id: cp.id,
                        description: cp.description,
                        timestamp: cp.timestamp,
                        fileCount: cp.files.length
                    }))
                });
            }
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to get checkpoints: ${error}`);
        }
    }

    private async handleCopyCode(code: string): Promise<void> {
        await vscode.env.clipboard.writeText(code);
        vscode.window.showInformationMessage('Code copied to clipboard!');
    }

    private async handleOpenFile(filename: string): Promise<void> {
        try {
            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            if (!workspaceFolder) {
                vscode.window.showErrorMessage('No workspace folder open');
                return;
            }

            const filePath = vscode.Uri.joinPath(workspaceFolder.uri, filename);

            try {
                // Try to open existing file
                const document = await vscode.workspace.openTextDocument(filePath);
                await vscode.window.showTextDocument(document);
                vscode.window.showInformationMessage(`Opened ${filename}`);
            } catch (error) {
                // File doesn't exist, ask if user wants to create it
                const choice = await vscode.window.showQuickPick(
                    ['Create new file', 'Cancel'],
                    { placeHolder: `File ${filename} doesn't exist. What would you like to do?` }
                );

                if (choice === 'Create new file') {
                    await vscode.workspace.fs.writeFile(filePath, Buffer.from('', 'utf8'));
                    const document = await vscode.workspace.openTextDocument(filePath);
                    await vscode.window.showTextDocument(document);
                    vscode.window.showInformationMessage(`Created and opened ${filename}`);
                }
            }
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to open file: ${error}`);
        }
    }

    private async handleDeleteFile(filename: string): Promise<void> {
        try {
            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            if (!workspaceFolder) {
                vscode.window.showErrorMessage('No workspace folder open');
                return;
            }

            const filePath = vscode.Uri.joinPath(workspaceFolder.uri, filename);

            // Check if file exists
            try {
                await vscode.workspace.fs.stat(filePath);
            } catch (error) {
                vscode.window.showWarningMessage(`File ${filename} doesn't exist`);
                return;
            }

            // Confirm deletion
            const choice = await vscode.window.showWarningMessage(
                `Are you sure you want to delete ${filename}?`,
                { modal: true },
                'Delete',
                'Cancel'
            );

            if (choice === 'Delete') {
                await vscode.workspace.fs.delete(filePath);
                vscode.window.showInformationMessage(`Deleted ${filename}`);
            }
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to delete file: ${error}`);
        }
    }

    private async handleFileOperation(operation: string, filename: string, content: string): Promise<void> {
        try {
            const workspaceFolders = vscode.workspace.workspaceFolders;
            if (!workspaceFolders) {
                vscode.window.showErrorMessage('No workspace folder open');
                return;
            }

            const filePath = vscode.Uri.joinPath(workspaceFolders[0].uri, filename);

            switch (operation.toLowerCase()) {
                case 'create':
                    await this.handleCreateFile(filename, content, '');
                    break;

                case 'edit':
                    try {
                        await vscode.workspace.fs.stat(filePath);
                        await vscode.workspace.fs.writeFile(filePath, Buffer.from(content, 'utf8'));

                        // Open the file to show changes
                        const document = await vscode.workspace.openTextDocument(filePath);
                        await vscode.window.showTextDocument(document);

                        vscode.window.showInformationMessage(`File ${filename} updated successfully!`);
                    } catch {
                        vscode.window.showErrorMessage(`File ${filename} not found`);
                    }
                    break;

                case 'delete':
                    await this.handleDeleteFile(filename);
                    break;

                default:
                    vscode.window.showErrorMessage(`Unknown operation: ${operation}`);
            }
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to execute ${operation}: ${error}`);
        }
    }

    async clearHistory(): Promise<void> {
        await this.handleClearHistory();
    }

    getTreeDataProvider(): vscode.TreeDataProvider<any> {
        return new ModernChatTreeDataProvider();
    }

    dispose(): void {
        // Abort any ongoing streaming
        if (this.streamingAbortController) {
            this.streamingAbortController.abort();
        }

        // Save session state
        this.persistSession();

        // Clear active session marker
        this.clearActiveSession();

        if (this.chatPanel) {
            this.chatPanel.dispose();
        }
    }

    /**
     * Generate a unique session ID
     */
    private generateSessionId(): string {
        return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }

    /**
     * Load persisted session data
     */
    private loadPersistedSession(): void {
        try {
            // For now, just start with a fresh session
            // We can add persistence later if needed
            this.conversationHistory = [];
            this.currentMode = 'agent';
        } catch (error) {
            console.error('Failed to load persisted session:', error);
        }
    }

    /**
     * Persist current session data
     */
    private persistSession(): void {
        try {
            const sessionData = {
                sessionId: this.sessionId,
                conversationHistory: this.conversationHistory,
                currentMode: this.currentMode,
                timestamp: Date.now()
            };
            this.context.globalState.update(`augment_session_${this.sessionId}`, sessionData);
        } catch (error) {
            console.error('Failed to persist session:', error);
        }
    }

    /**
     * Check if another chat instance is already active
     */
    private isAnotherInstanceActive(): boolean {
        const activeSession = this.context.globalState.get<string>('augment_active_session');
        return activeSession !== undefined && activeSession !== this.sessionId;
    }

    /**
     * Mark this session as active
     */
    private markSessionActive(): void {
        this.context.globalState.update('augment_active_session', this.sessionId);
    }

    /**
     * Clear active session marker
     */
    private clearActiveSession(): void {
        this.context.globalState.update('augment_active_session', undefined);
    }

    /**
     * Handle streaming interruption and recovery
     */
    private async handleStreamingInterruption(error: any): Promise<void> {
        console.error('Streaming interrupted:', error);

        this.isStreaming = false;

        if (this.chatPanel) {
            this.chatPanel.webview.postMessage({
                type: 'streamingError',
                message: 'Streaming was interrupted. You can retry your request.'
            });
        }

        // Clean up abort controller
        this.streamingAbortController = undefined;
    }

    private getWebviewContent(): string {
        // This will be implemented in the next part
        return this.getModernHtmlTemplate();
    }

    private getModernHtmlTemplate(): string {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Augment Assistant</title>
    <style>
        :root {
            --augment-primary: #3D855E;
            --augment-primary-light: #4A9B6E;
            --augment-primary-dark: #2F6B4A;
            --augment-secondary: #6B7280;
            --augment-success: #10B981;
            --augment-warning: #F59E0B;
            --augment-danger: #EF4444;
            --augment-info: #3B82F6;
            --augment-border-radius: 12px;
            --augment-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --augment-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --augment-gradient: linear-gradient(135deg, var(--augment-primary) 0%, var(--augment-primary-light) 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.5;
            color: var(--vscode-foreground);
            background: var(--vscode-editor-background);
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .augment-header {
            background: var(--augment-gradient);
            color: white;
            padding: 16px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: var(--augment-shadow-lg);
            z-index: 100;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .augment-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 18px;
            font-weight: 600;
        }

        .augment-logo::before {
            content: "⚡";
            font-size: 24px;
            filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.3));
        }

        .mode-selector {
            display: flex;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 4px;
            gap: 4px;
        }

        .mode-btn {
            padding: 6px 12px;
            border: none;
            background: transparent;
            color: white;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .mode-btn.active {
            background: rgba(255, 255, 255, 0.2);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .mode-btn:hover {
            background: rgba(255, 255, 255, 0.15);
        }

        .chat-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: var(--vscode-editor-background);
            overflow: hidden;
        }

        .chat-toolbar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 20px;
            background: var(--vscode-sideBar-background);
            border-bottom: 1px solid var(--vscode-panel-border);
        }

        .context-toggle {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
            color: var(--vscode-descriptionForeground);
        }

        .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: var(--vscode-button-secondaryBackground);
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .toggle-switch.active {
            background: var(--augment-primary);
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .toggle-switch.active::after {
            transform: translateX(20px);
        }

        .checkpoint-controls {
            display: flex;
            gap: 8px;
        }

        .btn {
            background: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 6px;
            padding: 6px 12px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn:hover {
            background: var(--vscode-button-hoverBackground);
            transform: translateY(-1px);
        }

        .btn.btn-primary {
            background: var(--augment-primary);
            color: white;
        }

        .btn.btn-primary:hover {
            background: var(--augment-primary-dark);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: var(--vscode-editor-background);
            scroll-behavior: smooth;
            position: relative;
        }

        .messages-container::-webkit-scrollbar {
            width: 8px;
        }

        .messages-container::-webkit-scrollbar-track {
            background: transparent;
        }

        .messages-container::-webkit-scrollbar-thumb {
            background: var(--vscode-scrollbarSlider-background);
            border-radius: 4px;
        }

        .messages-container::-webkit-scrollbar-thumb:hover {
            background: var(--vscode-scrollbarSlider-hoverBackground);
        }

        .message {
            margin: 16px 0;
            display: flex;
            gap: 12px;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            flex-shrink: 0;
            margin-top: 4px;
        }

        .user-avatar {
            background: var(--augment-primary);
            color: white;
        }

        .assistant-avatar {
            background: var(--augment-gradient);
            color: white;
        }

        .message-content {
            flex: 1;
            min-width: 0;
        }

        .message-bubble {
            background: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            border-radius: 12px;
            padding: 12px 16px;
            line-height: 1.5;
            position: relative;
        }

        .user-message .message-bubble {
            background: var(--augment-primary);
            color: white;
            border-color: var(--augment-primary);
        }

        .assistant-message .message-bubble {
            background: var(--vscode-editor-background);
            border-color: var(--vscode-panel-border);
        }

        .code-block {
            margin: 12px 0;
            border-radius: 8px;
            overflow: hidden;
            background: var(--vscode-textCodeBlock-background);
            border: 1px solid var(--vscode-panel-border);
        }

        .code-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: var(--vscode-tab-inactiveBackground);
            border-bottom: 1px solid var(--vscode-panel-border);
            font-size: 12px;
            font-weight: 500;
        }

        .code-language {
            color: var(--vscode-descriptionForeground);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .code-actions {
            display: flex;
            gap: 6px;
        }

        .code-action-btn {
            background: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .code-action-btn:hover {
            background: var(--vscode-button-secondaryHoverBackground);
            transform: translateY(-1px);
        }

        .code-content {
            padding: 12px;
            font-family: var(--vscode-editor-font-family);
            font-size: 13px;
            line-height: 1.4;
            overflow-x: auto;
            white-space: pre;
            color: var(--vscode-editor-foreground);
        }

        .agent-actions {
            margin: 12px 0;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .action-btn {
            background: var(--augment-primary);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .action-btn:hover {
            background: var(--augment-primary-dark);
            transform: translateY(-1px);
            box-shadow: var(--augment-shadow);
        }

        .action-btn.create { background: var(--augment-success); }
        .action-btn.modify { background: var(--augment-warning); }
        .action-btn.delete { background: var(--augment-danger); }
        .action-btn.command { background: var(--augment-info); }

        .input-area {
            padding: 16px 20px;
            background: var(--vscode-sideBar-background);
            border-top: 1px solid var(--vscode-panel-border);
        }

        .input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        .message-input {
            width: 100%;
            background: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border: 1px solid var(--vscode-input-border);
            border-radius: 12px;
            padding: 12px 16px;
            font-family: inherit;
            font-size: 14px;
            resize: none;
            min-height: 44px;
            max-height: 120px;
            transition: all 0.2s ease;
            line-height: 1.4;
        }

        .message-input:focus {
            outline: none;
            border-color: var(--augment-primary);
            box-shadow: 0 0 0 2px rgba(61, 133, 94, 0.2);
        }

        .send-button {
            width: 44px;
            height: 44px;
            background: var(--augment-primary);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            flex-shrink: 0;
        }

        .send-button:hover:not(:disabled) {
            background: var(--augment-primary-dark);
            transform: scale(1.05);
            box-shadow: var(--augment-shadow);
        }

        .send-button:disabled {
            background: var(--vscode-button-secondaryBackground);
            color: var(--vscode-disabledForeground);
            cursor: not-allowed;
            transform: none;
        }

        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            color: var(--vscode-descriptionForeground);
            font-size: 13px;
            font-style: italic;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            background: var(--augment-primary);
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        .welcome-message {
            text-align: center;
            padding: 40px 20px;
            color: var(--vscode-descriptionForeground);
            border: 2px dashed var(--vscode-panel-border);
            border-radius: var(--augment-border-radius);
            background: var(--vscode-textCodeBlock-background);
        }

        .welcome-message h3 {
            color: var(--augment-primary);
            margin-bottom: 16px;
            font-size: 20px;
        }

        .welcome-message ul {
            text-align: left;
            max-width: 400px;
            margin: 20px auto;
        }

        .welcome-message li {
            margin: 8px 0;
            padding-left: 8px;
        }

        .code-block {
            background: var(--vscode-textCodeBlock-background);
            border: 2px solid var(--augment-primary);
            border-radius: var(--augment-border-radius);
            margin: 16px 0;
            overflow: hidden;
            box-shadow: var(--augment-shadow);
        }

        .code-header {
            background: var(--augment-gradient);
            color: white;
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 13px;
            font-weight: 600;
        }

        .code-actions {
            display: flex;
            gap: 8px;
        }

        .code-action-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            padding: 6px 12px;
            font-size: 11px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .code-action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .code-content {
            padding: 20px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
            overflow-x: auto;
            white-space: pre-wrap;
            background: var(--vscode-editor-background);
        }

        .file-operation {
            background: var(--vscode-notifications-background);
            border: 2px solid var(--augment-primary);
            border-radius: var(--augment-border-radius);
            margin: 16px 0;
            overflow: hidden;
            box-shadow: var(--augment-shadow);
            animation: slideIn 0.3s ease-out;
        }

        .file-operation.create {
            border-color: var(--augment-success);
        }

        .file-operation.modify {
            border-color: var(--augment-warning);
        }

        .file-operation.delete {
            border-color: var(--augment-danger);
        }

        .file-operation-header {
            background: var(--augment-gradient);
            color: white;
            padding: 12px 20px;
            font-weight: 600;
            font-size: 14px;
        }

        .file-operation.create .file-operation-header {
            background: linear-gradient(135deg, var(--augment-success) 0%, #20c997 100%);
        }

        .file-operation.modify .file-operation-header {
            background: linear-gradient(135deg, var(--augment-warning) 0%, #fd7e14 100%);
        }

        .file-operation.delete .file-operation-header {
            background: linear-gradient(135deg, var(--augment-danger) 0%, #e74c3c 100%);
        }

        .file-operation-actions {
            padding: 16px 20px;
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            background: var(--vscode-editor-background);
        }

        .btn-success {
            background: var(--augment-success) !important;
            color: white !important;
        }

        .btn-success:hover {
            background: #218838 !important;
        }

        .btn-warning {
            background: var(--augment-warning) !important;
            color: #212529 !important;
        }

        .btn-warning:hover {
            background: #e0a800 !important;
        }

        .btn-danger {
            background: var(--augment-danger) !important;
            color: white !important;
        }

        .btn-danger:hover {
            background: #c82333 !important;
        }

        .btn-secondary {
            background: var(--augment-secondary) !important;
            color: white !important;
        }

        .btn-secondary:hover {
            background: #5a6268 !important;
        }

        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            font-style: italic;
            opacity: 0.8;
            color: var(--augment-primary);
        }

        .typing-indicator::before {
            content: "";
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--augment-primary);
            animation: typing 1.4s infinite ease-in-out;
        }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 16px;
                padding: 16px;
            }

            .model-selector {
                width: 100%;
                justify-content: center;
            }

            .user-message, .assistant-message, .error {
                margin-left: 5%;
                margin-right: 5%;
            }

            .user-message::before, .assistant-message::before, .error::before {
                display: none;
            }

            .input-controls {
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
            }

            .btn-primary {
                width: 100% !important;
            }
        }

        /* Streaming Command Pipeline Styles */
        .streaming-mode {
            border: 2px solid var(--augment-primary);
            background: linear-gradient(135deg, rgba(61, 133, 94, 0.05) 0%, rgba(74, 155, 110, 0.05) 100%);
            animation: streamingPulse 2s infinite;
        }

        .streaming-complete {
            border: 2px solid var(--augment-success);
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(16, 185, 129, 0.1) 100%);
        }

        .streaming-error {
            border: 2px solid var(--augment-danger);
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.05) 0%, rgba(239, 68, 68, 0.1) 100%);
        }

        .streaming-header {
            margin-bottom: 16px;
            padding: 12px;
            background: rgba(61, 133, 94, 0.1);
            border-radius: 8px;
            border-left: 4px solid var(--augment-primary);
        }

        .streaming-title {
            font-weight: bold;
            font-size: 16px;
            color: var(--augment-primary);
            margin-bottom: 4px;
        }

        .streaming-status {
            font-size: 14px;
            color: var(--vscode-foreground);
            opacity: 0.8;
        }

        .streaming-commands {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .streaming-command {
            background: var(--vscode-editor-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 8px;
            padding: 12px;
            transition: all 0.3s ease;
            animation: commandSlideIn 0.5s ease;
        }

        .command-header {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }

        .command-icon {
            font-size: 18px;
            width: 24px;
            text-align: center;
        }

        .command-number {
            background: var(--augment-primary);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .command-action {
            flex: 1;
            color: var(--vscode-foreground);
        }

        .command-status {
            color: var(--augment-success);
            font-size: 16px;
        }

        .command-preview {
            margin-top: 8px;
            border-top: 1px solid var(--vscode-panel-border);
            padding-top: 8px;
        }

        .preview-header {
            font-size: 12px;
            color: var(--augment-primary);
            margin-bottom: 4px;
            font-weight: 500;
        }

        .preview-content {
            background: var(--vscode-textCodeBlock-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
            padding: 8px;
            font-size: 12px;
            max-height: 100px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        @keyframes streamingPulse {
            0%, 100% {
                border-color: var(--augment-primary);
                box-shadow: 0 0 0 0 rgba(61, 133, 94, 0.4);
            }
            50% {
                border-color: var(--augment-primary-light);
                box-shadow: 0 0 0 4px rgba(61, 133, 94, 0.1);
            }
        }

        @keyframes commandSlideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Mode Switch Suggestion Styles */
        .mode-switch-suggestion {
            background: linear-gradient(135deg, rgba(61, 133, 94, 0.1) 0%, rgba(74, 155, 110, 0.1) 100%);
            border: 1px solid var(--augment-primary);
            border-radius: 12px;
            padding: 1rem;
            margin: 1rem 0;
            animation: commandSlideIn 0.5s ease;
        }

        .suggestion-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.75rem;
        }

        .suggestion-icon {
            font-size: 1.25rem;
        }

        .suggestion-title {
            font-weight: bold;
            color: var(--augment-primary);
        }

        .suggestion-content p {
            margin-bottom: 1rem;
            color: var(--vscode-foreground);
            line-height: 1.4;
        }

        .suggestion-content .btn {
            background: var(--augment-primary);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: background 0.2s;
        }

        .suggestion-content .btn:hover {
            background: var(--augment-primary-light);
        }

        /* Terminal Command Styles */
        .command-terminal {
            margin-top: 8px;
            border-top: 1px solid var(--vscode-panel-border);
            padding-top: 8px;
        }

        .terminal-header {
            font-size: 12px;
            color: var(--augment-primary);
            margin-bottom: 4px;
            font-weight: 500;
        }

        .terminal-command {
            display: flex;
            align-items: center;
            gap: 8px;
            background: var(--vscode-terminal-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
            padding: 8px;
        }

        .terminal-command code {
            flex: 1;
            background: transparent;
            color: var(--vscode-terminal-foreground);
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .btn-terminal {
            background: var(--augment-primary);
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            font-weight: 500;
            transition: background 0.2s;
        }

        .btn-terminal:hover {
            background: var(--augment-primary-light);
        }

        /* Chat Command Styles */
        .command-chat {
            margin-top: 8px;
            padding: 8px;
            background: rgba(61, 133, 94, 0.05);
            border-left: 3px solid var(--augment-primary);
            border-radius: 0 4px 4px 0;
        }

        .chat-content {
            color: var(--vscode-foreground);
            font-style: italic;
            line-height: 1.4;
        }


    </style>
</head>
<body>
    <!-- AugmentCode-style Header -->
    <div class="augment-header">
        <div class="augment-logo">Augment Assistant</div>
        <div class="mode-selector">
            <button class="mode-btn active" data-mode="agent">🤖 Agent</button>
            <button class="mode-btn" data-mode="chat">💬 Chat</button>
            <button class="mode-btn" data-mode="agent-auto">⚡ Auto</button>
        </div>
    </div>

    <!-- Main Chat Interface -->
    <div class="chat-main">
        <!-- Toolbar -->
        <div class="chat-toolbar">
            <div class="context-toggle">
                <span>Include Context</span>
                <div class="toggle-switch active" id="context-toggle"></div>
            </div>
            <div class="checkpoint-controls">
                <button class="btn" onclick="createCheckpoint()">💾 Checkpoint</button>
                <button class="btn" onclick="showCheckpoints()">📋 History</button>
                <button class="btn" onclick="testStreaming()" style="background: var(--augment-primary); color: white;">🧪 Test Streaming</button>
            </div>
        </div>

        <!-- Messages Area -->
        <div class="messages-container" id="messages">
            <div class="welcome-message">
                <h3>⚡ Welcome to Augment Assistant!</h3>
                <p>Your AI pair programmer with streaming command pipeline:</p>
                <ul>
                    <li>🧠 Deep codebase understanding with 200K context</li>
                    <li>🎯 Next Edit suggestions for complex changes</li>
                    <li>💾 Code checkpoints with automatic rollbacks</li>
                    <li>🚀 Streaming multi-step task execution</li>
                </ul>
                <p><strong>🚀 Try Streaming Mode with:</strong></p>
                <ul>
                    <li>"Create a React chat component with TypeScript"</li>
                    <li>"Build a REST API with authentication"</li>
                    <li>"Set up a Node.js project with Express"</li>
                    <li>"Create a simple todo app with React"</li>
                </ul>
                <p>Or click the <strong>🧪 Test Streaming</strong> button to see it in action!</p>
            </div>
        </div>

        <!-- Input Area -->
        <div class="input-area">
            <div class="input-container">
                <div class="input-wrapper">
                    <textarea
                        id="message-input"
                        class="message-input"
                        placeholder="Ask me anything about your code..."
                        rows="1"
                    ></textarea>
                </div>
                <button class="send-button" id="send-button" onclick="sendMessage()" disabled>
                    ➤
                </button>
            </div>
            <div class="typing-indicator" id="typing-indicator" style="display: none;">
                <span>Augment is thinking</span>
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        let currentAssistantMessage = null;
        let currentMode = 'agent';
        let isStreaming = false;

        // Add this function call when the webview loads
        window.addEventListener('load', function() {
            console.log('Webview loaded, resetting state...');
            isStreaming = false;
            updateSendButton();
        });

        // Initialize interface
        document.addEventListener('DOMContentLoaded', function() {
            initializeInterface();
            setupEventListeners();
        });

        // Also initialize immediately if DOM is already loaded
        if (document.readyState === 'loading') {
            // DOM is still loading, event listener above will handle it
        } else {
            // DOM is already loaded
            initializeInterface();
            setupEventListeners();
        }

        function initializeInterface() {
            console.log('Initializing interface, currentMode:', currentMode, 'isStreaming:', isStreaming);

            // Force reset streaming state on initialization
            isStreaming = false;

            // Initialize mode buttons with click handlers
            document.querySelectorAll('.mode-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.dataset.mode === currentMode) {
                    btn.classList.add('active');
                }

                // Add click event listener for mode switching
                btn.addEventListener('click', function() {
                    if (!isStreaming) {
                        switchToMode(this.dataset.mode);
                    } else {
                        showToast('Cannot switch modes while the AI is processing.', 'warning');
                    }
                });

                console.log('Mode button:', btn.dataset.mode, 'active:', btn.classList.contains('active'));
            });

            // Auto-resize textarea
            const textarea = document.getElementById('message-input');
            if (textarea) {
                // Multiple event listeners to ensure send button updates
                ['input', 'keyup', 'paste', 'change'].forEach(eventType => {
                    textarea.addEventListener(eventType, function() {
                        if (eventType === 'input') {
                            this.style.height = 'auto';
                            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
                        }
                        updateSendButton();
                    });
                });
            } else {
                console.error('Message input textarea not found!');
            }

            // Context toggle
            const contextToggle = document.getElementById('context-toggle');
            contextToggle.addEventListener('click', function() {
                this.classList.toggle('active');
            });

            // Initialize send button state
            updateSendButton();

            // Force check send button state after a short delay
            setTimeout(() => {
                const input = document.getElementById('message-input');
                const button = document.getElementById('send-button');
                console.log('Send button check - input value:', input?.value, 'isStreaming:', isStreaming, 'button disabled:', button?.disabled);
                updateSendButton();
            }, 100);

            // Add reset button for debugging
            addResetButton();

            // Periodic check to ensure send button works (every 2 seconds)
            setInterval(() => {
                const input = document.getElementById('message-input');
                const button = document.getElementById('send-button');

                if (input && button && input.value.trim() && button.disabled && !isStreaming) {
                    console.log('Send button was incorrectly disabled, fixing...');
                    updateSendButton();
                }
            }, 2000);

            // Mode buttons
            document.querySelectorAll('.mode-btn').forEach(btn => {
                console.log('Adding click listener to mode button:', btn.dataset.mode);
                btn.addEventListener('click', function() {
                    console.log('Mode button clicked:', this.dataset.mode, 'isStreaming:', isStreaming);

                    // Don't allow mode switching while streaming
                    if (isStreaming) {
                        showToast('Cannot switch modes while streaming. Please wait or reset.');
                        return;
                    }

                    document.querySelectorAll('.mode-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    currentMode = this.dataset.mode;
                    console.log('Sending setMode message:', currentMode);
                    vscode.postMessage({ type: 'setMode', mode: currentMode });
                    showToast(\`Switched to \${currentMode} mode\`);
                });
            });

            // Enter key handling
            textarea.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    if (!isStreaming && this.value.trim()) {
                        sendMessage();
                    }
                }
            });
        }

        function setupEventListeners() {
            // Handle messages from extension
            window.addEventListener('message', event => {
                const message = event.data;
                console.log('Webview received message:', message);

                switch (message.type) {
                    case 'userMessage':
                        displayUserMessage(message.message);
                        break;
                    case 'assistantMessageStart':
                        startAssistantMessage();
                        break;
                    case 'assistantMessageChunk':
                        appendToAssistantMessage(message.content);
                        break;
                    case 'assistantMessageEnd':
                        endAssistantMessage();
                        isStreaming = false;
                        updateSendButton();
                        updateModeButtonsState(false);
                        break;
                    case 'agentActions':
                        displayAgentActions(message.actions);
                        break;
                    case 'nextEdits':
                        displayNextEdits(message.edits);
                        break;
                    case 'error':
                        displayError(message.message);
                        break;
                    case 'historyCleared':
                        clearMessages();
                        break;
                    case 'modeChanged':
                        currentMode = message.mode;
                        updateModeButtons(message.mode);
                        break;
                    case 'setStreamingStatus':
                        isStreaming = message.status;
                        updateSendButton();
                        updateModeButtonsState(message.status);
                        break;
                    case 'streamingError':
                        displayStreamingError(message.message);
                        isStreaming = false;
                        updateSendButton();
                        updateModeButtonsState(false);
                        break;
                    case 'assistantMessageEnd':
                        endAssistantMessage();
                        isStreaming = false;
                        updateSendButton();
                        updateModeButtonsState(false);
                        break;
                    case 'streamingComplete':
                        endStreamingMode(message.message);
                        isStreaming = false;
                        updateSendButton();
                        updateModeButtonsState(false);
                        break;
                    case 'checkpointCreated':
                        showToast('Checkpoint created: ' + message.description);
                        break;
                    case 'checkpointRestored':
                        showToast('Checkpoint restored successfully');
                        break;
                    case 'fileOperationExecuted':
                        if (message.success) {
                            showToast('✅ ' + message.operation + ' file: ' + message.filename);
                        } else {
                            showToast('❌ Failed to ' + message.operation.toLowerCase() + ' file: ' + message.filename);
                        }
                        break;
                    case 'streamingStart':
                        startStreamingMode(message.message);
                        break;
                    case 'streamingCommand':
                        displayStreamingCommand(message.command);
                        break;
                    case 'streamingComplete':
                        endStreamingMode(message.message);
                        isStreaming = false;
                        updateSendButton();
                        updateModeButtonsState(false);
                        break;
                    case 'streamingError':
                        displayStreamingError(message.message);
                        isStreaming = false;
                        updateSendButton();
                        updateModeButtonsState(false);
                        break;
                    case 'suggestModeSwitch':
                        displayModeSwitchSuggestion(message.mode, message.reason);
                        break;
                    case 'runTerminalCommand':
                        executeTerminalCommand(message.command);
                        break;
                }
            });
        }

        function sendMessage() {
            const input = document.getElementById('message-input');
            const contextToggle = document.getElementById('context-toggle');
            const streamingToggle = document.getElementById('streaming-toggle');
            const includeContext = contextToggle.classList.contains('active');
            const useStreaming = streamingToggle.classList.contains('active');
            const text = input.value.trim();

            if (!text || isStreaming) return;

            isStreaming = true;

            if (useStreaming) {
                // Disable input during streaming
                input.disabled = true;
                input.placeholder = 'Agent is working...';

                vscode.postMessage({
                    type: 'sendStreamingMessage',
                    text: text,
                    includeContext: includeContext
                });
            } else {
                showTypingIndicator();

                vscode.postMessage({
                    type: 'sendMessage',
                    text: text,
                    includeContext: includeContext
                });
            }

            input.value = '';
            input.style.height = 'auto';
            updateSendButton();
        }

        function clearHistory() {
            vscode.postMessage({ type: 'clearHistory' });
        }

        function refreshModels() {
            console.log('Requesting models...');
            vscode.postMessage({ type: 'getModels' });
        }

        function updateModelSelector(models) {
            console.log('updateModelSelector called with models:', models);
            const select = document.getElementById('model-select');
            if (!select) {
                console.error('model-select element not found!');
                return;
            }

            const currentValue = select.value;
            console.log('Current select value:', currentValue);

            select.innerHTML = '<option value="">Select a model...</option>';
            models.forEach(model => {
                const option = document.createElement('option');
                option.value = model;
                option.textContent = model;
                select.appendChild(option);
                console.log('Added model option:', model);
            });

            if (models.includes(currentValue)) {
                select.value = currentValue;
            }

            console.log('Model selector updated. Total options:', select.options.length);
        }

        function displayUserMessage(message) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message user-message';
            messageDiv.innerHTML =
                '<div class="message-avatar user-avatar">👤</div>' +
                '<div class="message-content">' +
                    '<div class="message-bubble">' + escapeHtml(message) + '</div>' +
                '</div>';
            messagesDiv.appendChild(messageDiv);
            scrollToBottom();
        }

        function startAssistantMessage() {
            hideTypingIndicator();
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message assistant-message';
            messageDiv.innerHTML =
                '<div class="message-avatar assistant-avatar">⚡</div>' +
                '<div class="message-content">' +
                    '<div class="message-bubble"></div>' +
                '</div>';
            messagesDiv.appendChild(messageDiv);
            currentAssistantMessage = messageDiv.querySelector('.message-bubble');
            scrollToBottom();
        }

        function appendToAssistantMessage(content) {
            if (currentAssistantMessage) {
                currentAssistantMessage.innerHTML += formatMessage(content);
                scrollToBottom();
            }
        }

        function endAssistantMessage() {
            isStreaming = false;
            currentAssistantMessage = null;
            updateSendButton();
        }

        function displayAgentActions(actions) {
            if (!currentAssistantMessage || !actions.length) return;

            const actionsDiv = document.createElement('div');
            actionsDiv.className = 'agent-actions';

            actions.forEach(action => {
                const btn = document.createElement('button');
                btn.className = 'action-btn ' + action.type;
                btn.innerHTML = getActionIcon(action.type) + ' ' + action.description;
                btn.onclick = () => executeAction(action);
                actionsDiv.appendChild(btn);
            });

            currentAssistantMessage.appendChild(actionsDiv);
        }

        function displayNextEdits(edits) {
            // Implementation for next edit suggestions
            console.log('Next edits:', edits);
        }

        function displayError(message) {
            const messagesDiv = document.getElementById('messages');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'message error';
            errorDiv.textContent = message;
            messagesDiv.appendChild(errorDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
            document.getElementById('send-button').disabled = false;
            updateSendButton();
        }

        function clearMessages() {
            const messagesDiv = document.getElementById('messages');
            messagesDiv.innerHTML = '<div class="welcome-message"><h3>Welcome to Augment Assistant!</h3><p>Your AI pair programmer with advanced capabilities. Start by asking me anything about your code!</p></div>';



        }

        function formatMessage(content) {
            // Simple formatting without regex
            if (typeof content !== 'string' || content.length === 0) {
                return content;
            }
            return content.replace(/\\n/g, '<br>');
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }



        function copyCode(codeId) {
            const codeElement = document.getElementById(codeId);
            if (codeElement) {
                navigator.clipboard.writeText(codeElement.textContent);
                showToast('Code copied to clipboard! 📋');
            }
        }

        function applyCode(codeId, language) {
            const codeElement = document.getElementById(codeId);
            if (codeElement) {
                vscode.postMessage({
                    type: 'applyCode',
                    code: codeElement.textContent,
                    language: language
                });
                showToast('Code applied! ✨');
            }
        }

        function createFile(codeId, language) {
            const codeElement = document.getElementById(codeId);
            if (codeElement) {
                const filename = prompt('Enter filename:', getDefaultFilename(language));
                if (filename) {
                    vscode.postMessage({
                        type: 'createFile',
                        filename: filename,
                        content: codeElement.textContent,
                        language: language
                    });
                    showToast('File creation requested! 📁');
                }
            }
        }

        function createFileFromSuggestion(filename) {
            // Find the next code block after this file operation
            const fileOps = document.querySelectorAll('.file-operation.create');
            let nextCodeBlock = null;

            for (let fileOp of fileOps) {
                if (fileOp.textContent.includes(filename)) {
                    nextCodeBlock = fileOp.nextElementSibling;
                    while (nextCodeBlock && !nextCodeBlock.classList.contains('code-block')) {
                        nextCodeBlock = nextCodeBlock.nextElementSibling;
                    }
                    break;
                }
            }

            if (nextCodeBlock) {
                const codeContent = nextCodeBlock.querySelector('.code-content');
                if (codeContent) {
                    vscode.postMessage({
                        type: 'createFile',
                        filename: filename,
                        content: codeContent.textContent,
                        language: 'auto'
                    });
                    showToast('Creating file: ' + filename + ' 📁');
                    return;
                }
            }

            // Fallback: create empty file
            vscode.postMessage({
                type: 'createFile',
                filename: filename,
                content: '',
                language: 'auto'
            });
            showToast('Creating empty file: ' + filename + ' 📁');
        }

        function openFileForEdit(filename) {
            vscode.postMessage({
                type: 'openFile',
                filename: filename
            });
            showToast('Opening file: ' + filename + ' 📝');
        }

        function deleteFile(filename) {
            if (confirm('Are you sure you want to delete ' + filename + '?')) {
                vscode.postMessage({
                    type: 'deleteFile',
                    filename: filename
                });
                showToast('Deleting file: ' + filename + ' 🗑️');
            }
        }

        function declineSuggestion() {
            showToast('Suggestion declined ❌');
        }

        // Utility functions
        function updateSendButton() {
            const input = document.getElementById('message-input');
            const button = document.getElementById('send-button');

            if (!input || !button) return;

            const hasText = input.value.trim().length > 0;
            const shouldEnable = hasText && !isStreaming;

            button.disabled = !shouldEnable;
            
            // Force visual update
            if (shouldEnable) {
                button.style.opacity = '1';
                button.style.cursor = 'pointer';
                button.removeAttribute('disabled');
            } else {
                button.style.opacity = '0.5';
                button.style.cursor = 'not-allowed';
                button.setAttribute('disabled', 'true');
            }
        }

        // Add a function to force reset the streaming state
        function resetStreamingState() {
            isStreaming = false;
            const input = document.getElementById('message-input');
            if (input) {
                input.disabled = false;
                input.placeholder = 'Ask me anything about your code...';
            }
            updateSendButton();
            hideTypingIndicator();

            // Re-enable mode buttons
            document.querySelectorAll('.mode-btn').forEach(btn => {
                btn.disabled = false;
                btn.style.opacity = '1';
                btn.style.pointerEvents = 'auto';
            });

            console.log('Streaming state reset');
        }

        // Add a manual reset button for debugging
        function addResetButton() {
            const toolbar = document.querySelector('.chat-toolbar');
            if (toolbar && !document.getElementById('reset-btn')) {
                const resetBtn = document.createElement('button');
                resetBtn.id = 'reset-btn';
                resetBtn.className = 'btn';
                resetBtn.innerHTML = '🔄 Reset';
                resetBtn.title = 'Reset streaming state if stuck';
                resetBtn.onclick = resetStreamingState;
                toolbar.appendChild(resetBtn);
            }
        }

        // Global debug function - can be called from browser console
        window.debugAugment = function() {
            console.log('=== AUGMENT DEBUG INFO ===');
            console.log('currentMode:', currentMode);
            console.log('isStreaming:', isStreaming);

            const input = document.getElementById('message-input');
            const button = document.getElementById('send-button');
            console.log('Input element:', input);
            console.log('Input value:', input?.value);
            console.log('Input disabled:', input?.disabled);
            console.log('Button element:', button);
            console.log('Button disabled:', button?.disabled);

            console.log('Mode buttons:');
            document.querySelectorAll('.mode-btn').forEach(btn => {
                console.log('  ', btn.dataset.mode, 'active:', btn.classList.contains('active'));
            });

            console.log('=== FORCING RESET ===');
            resetStreamingState();
            updateSendButton();
            console.log('Reset complete');
        };

        // Force enable send button - can be called from console
        window.forceEnableSend = function() {
            console.log('Force enabling send button...');
            isStreaming = false;
            const input = document.getElementById('message-input');
            const button = document.getElementById('send-button');

            if (input && button) {
                button.disabled = false;
                button.style.opacity = '1';
                button.style.cursor = 'pointer';
                console.log('Send button force-enabled!');
                updateSendButton();
            } else {
                console.error('Could not find input or button elements');
            }
        };

        function showTypingIndicator() {
            document.getElementById('typing-indicator').style.display = 'flex';
        }

        function hideTypingIndicator() {
            document.getElementById('typing-indicator').style.display = 'none';
        }

        function scrollToBottom() {
            const messagesDiv = document.getElementById('messages');
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function getActionIcon(type) {
            const icons = {
                'create_file': '📁',
                'modify_file': '✏️',
                'delete_file': '🗑️',
                'run_command': '⚡',
                'apply_code': '✨'
            };
            return icons[type] || '🔧';
        }

        function executeAction(action) {
            vscode.postMessage({
                type: 'executeAction',
                action: action
            });
        }

        function createCheckpoint() {
            const description = prompt('Checkpoint description:', 'Manual checkpoint');
            if (description) {
                vscode.postMessage({
                    type: 'createCheckpoint',
                    description: description
                });
            }
        }

        function showCheckpoints() {
            vscode.postMessage({ type: 'getCheckpoints' });
        }

        function testStreaming() {
            vscode.postMessage({ type: 'testStreaming' });
        }

        function displayModeSwitchSuggestion(mode, reason) {
            const messagesDiv = document.getElementById('messages');
            const suggestionDiv = document.createElement('div');
            suggestionDiv.className = 'mode-switch-suggestion';
            suggestionDiv.innerHTML =
                '<div class="suggestion-header">' +
                    '<span class="suggestion-icon">💡</span>' +
                    '<span class="suggestion-title">Mode Switch Suggestion</span>' +
                '</div>' +
                '<div class="suggestion-content">' +
                    '<p>' + reason + '</p>' +
                    '<button class="btn btn-primary" onclick="switchToMode(\'' + mode + '\')">' +
                        'Switch to ' + mode.charAt(0).toUpperCase() + mode.slice(1) + ' Mode' +
                    '</button>' +
                '</div>';

            messagesDiv.appendChild(suggestionDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function updateModeButtons(activeMode) {
            document.querySelectorAll('.mode-btn').forEach(button => {
                button.classList.remove('active'); // Remove from all first
                if (button.dataset.mode === activeMode) {
                    button.classList.add('active');
                }
            });

            // Update input placeholder based on mode
            const messageInput = document.getElementById('message-input');
            if (messageInput) {
                if (activeMode === 'chat') {
                    messageInput.placeholder = 'Chat with the assistant...';
                } else if (activeMode === 'agent') {
                    messageInput.placeholder = 'Describe a task for the agent...';
                } else if (activeMode === 'agent-auto') {
                    messageInput.placeholder = 'Describe a task for autonomous execution...';
                }
            }
        }

        function updateModeButtonsState(isCurrentlyStreaming) {
            document.querySelectorAll('.mode-btn').forEach(button => {
                button.disabled = isCurrentlyStreaming;
                if (isCurrentlyStreaming) {
                    button.style.opacity = '0.5';
                    button.style.pointerEvents = 'none';
                } else {
                    button.style.opacity = '1';
                    button.style.pointerEvents = 'auto';
                }
            });
        }

        function switchToMode(mode) {
            if (isStreaming) {
                showToast('Cannot switch modes while the AI is processing.', 'warning');
                return;
            }

            // Send to backend
            vscode.postMessage({ type: 'switchMode', mode: mode });
            
            // Update current mode immediately for UI responsiveness
            currentMode = mode;
            updateModeButtons(mode);
            
            // Show feedback
            const modeNames = {
                'chat': '💬 Chat Mode',
                'agent': '🤖 Agent Mode', 
                'agent-auto': '⚡ Agent Auto Mode'
            };
            showToast('Switched to ' + (modeNames[mode] || mode));
        }

        function executeTerminalCommand(command) {
            vscode.postMessage({ type: 'runTerminalCommand', command: command });
            showToast('Executing: ' + command);
        }

        function showToast(message, type = 'info') {
            // Simple toast notification
            const toast = document.createElement('div');

            let backgroundColor = 'var(--augment-primary)';
            if (type === 'warning') {
                backgroundColor = 'var(--augment-warning)';
            } else if (type === 'error') {
                backgroundColor = 'var(--augment-danger)';
            } else if (type === 'success') {
                backgroundColor = 'var(--augment-success)';
            }

            toast.style.cssText =
                'position: fixed;' +
                'top: 20px;' +
                'right: 20px;' +
                'background: ' + backgroundColor + ';' +
                'color: white;' +
                'padding: 12px 16px;' +
                'border-radius: 8px;' +
                'z-index: 1000;' +
                'animation: slideIn 0.3s ease;';
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        function getDefaultFilename(language) {
            const extensions = {
                'javascript': 'script.js',
                'typescript': 'script.ts',
                'python': 'script.py',
                'java': 'Script.java',
                'cpp': 'script.cpp',
                'c': 'script.c',
                'html': 'index.html',
                'css': 'styles.css',
                'json': 'data.json'
            };
            return extensions[language] || 'file.txt';
        }

        // NEW: Streaming Command Pipeline UI Functions
        let streamingContainer = null;
        let streamingCommandCount = 0;

        function startStreamingMode(message) {
            const messagesDiv = document.getElementById('messages');

            // Create streaming container
            streamingContainer = document.createElement('div');
            streamingContainer.className = 'message assistant-message streaming-mode';
            streamingContainer.innerHTML =
                '<div class="message-avatar assistant-avatar">🤖</div>' +
                '<div class="message-content">' +
                    '<div class="streaming-header">' +
                        '<div class="streaming-title">🚀 Agent Processing</div>' +
                        '<div class="streaming-status">' + message + '</div>' +
                    '</div>' +
                    '<div class="streaming-commands" id="streaming-commands"></div>' +
                '</div>';

            messagesDiv.appendChild(streamingContainer);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;

            streamingCommandCount = 0;
            isStreaming = true;
        }

        function displayStreamingCommand(command) {
            if (!streamingContainer) return;

            const commandsDiv = document.getElementById('streaming-commands');
            streamingCommandCount++;

            const commandDiv = document.createElement('div');
            commandDiv.className = 'streaming-command';

            let icon = '📄';
            let actionText = command.task_description;

            switch (command.action) {
                case 'create_file':
                    icon = '📝';
                    break;
                case 'modify_file':
                    icon = '✏️';
                    break;
                case 'run_command':
                    icon = '⚡';
                    break;
                case 'delete_file':
                    icon = '🗑️';
                    break;
                case 'apply_code':
                    icon = '🔧';
                    break;
                case 'task_complete':
                    icon = '✅';
                    break;
            }

            commandDiv.innerHTML =
                '<div class="command-header">' +
                    '<span class="command-icon">' + icon + '</span>' +
                    '<span class="command-number">#' + streamingCommandCount + '</span>' +
                    '<span class="command-action">' + actionText + '</span>' +
                    '<span class="command-status">✅</span>' +
                '</div>';

            // Add file content preview for file operations
            if (command.content && (command.action === 'create_file' || command.action === 'modify_file')) {
                const previewDiv = document.createElement('div');
                previewDiv.className = 'command-preview';
                previewDiv.innerHTML =
                    '<div class="preview-header">📁 ' + (command.file_path || 'File') + '</div>' +
                    '<pre class="preview-content">' +
                        (command.content.length > 200 ?
                            command.content.substring(0, 200) + '...' :
                            command.content) +
                    '</pre>';
                commandDiv.appendChild(previewDiv);
            }

            // Add interactive terminal command for run_command actions
            if (command.action === 'run_command' && command.command) {
                const terminalDiv = document.createElement('div');
                terminalDiv.className = 'command-terminal';
                terminalDiv.innerHTML =
                    '<div class="terminal-header">💻 Terminal Command</div>' +
                    '<div class="terminal-command">' +
                        '<code>' + command.command + '</code>' +
                        '<button class="btn btn-terminal" onclick="executeTerminalCommand(\'' +
                            command.command.replace(/'/g, "\\'") + '\')">' +
                            'Run in Terminal' +
                        '</button>' +
                    '</div>';
                commandDiv.appendChild(terminalDiv);
            }

            // Add chat message display for chat actions
            if (command.action === 'chat') {
                const chatDiv = document.createElement('div');
                chatDiv.className = 'command-chat';
                chatDiv.innerHTML =
                    '<div class="chat-content">' + command.task_description + '</div>';
                commandDiv.appendChild(chatDiv);
            }

            commandsDiv.appendChild(commandDiv);

            // Update streaming status
            const statusDiv = streamingContainer.querySelector('.streaming-status');
            if (command.is_complete) {
                statusDiv.textContent = 'All tasks completed!';
            } else {
                statusDiv.textContent = 'Working on next task...';
            }

            // Scroll to bottom
            const messagesDiv = document.getElementById('messages');
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function endStreamingMode(message) {
            if (!streamingContainer) return;

            const statusDiv = streamingContainer.querySelector('.streaming-status');
            statusDiv.textContent = message;
            statusDiv.style.color = 'var(--augment-success)';

            // Remove streaming class
            streamingContainer.classList.remove('streaming-mode');
            streamingContainer.classList.add('streaming-complete');

            streamingContainer = null;
            isStreaming = false;

            // Re-enable input
            const input = document.getElementById('message-input');
            input.disabled = false;
            input.placeholder = 'Type your message...';

            // Update send button state
            updateSendButton();
        }

        function displayStreamingError(message) {
            if (!streamingContainer) return;

            const statusDiv = streamingContainer.querySelector('.streaming-status');
            statusDiv.textContent = 'Error: ' + message;
            statusDiv.style.color = 'var(--augment-danger)';

            streamingContainer.classList.remove('streaming-mode');
            streamingContainer.classList.add('streaming-error');

            streamingContainer = null;
            isStreaming = false;

            // Re-enable input
            const input = document.getElementById('message-input');
            input.disabled = false;
            input.placeholder = 'Type your message...';

            // Update send button state
            updateSendButton();
        }

        // Initialize when page loads
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeInterface);
        } else {
            initializeInterface();
        }
    </script>
</body>
</html>`;
    }
}

class ModernChatTreeDataProvider implements vscode.TreeDataProvider<ModernChatTreeItem> {
    getTreeItem(element: ModernChatTreeItem): vscode.TreeItem {
        return element;
    }

    getChildren(element?: ModernChatTreeItem): Thenable<ModernChatTreeItem[]> {
        if (!element) {
            return Promise.resolve([
                new ModernChatTreeItem('🚀 New Chat', vscode.TreeItemCollapsibleState.None, 'openChat'),
                new ModernChatTreeItem('🔄 Refresh Models', vscode.TreeItemCollapsibleState.None, 'refreshModels'),
                new ModernChatTreeItem('🗑️ Clear History', vscode.TreeItemCollapsibleState.None, 'clearHistory')
            ]);
        }
        return Promise.resolve([]);
    }
}

class ModernChatTreeItem extends vscode.TreeItem {
    constructor(
        public readonly label: string,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState,
        public readonly commandId?: string
    ) {
        super(label, collapsibleState);

        if (commandId) {
            this.command = {
                command: 'ollama-assistant.' + commandId,
                title: label
            };
        }
    }
}






















