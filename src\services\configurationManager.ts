import * as vscode from 'vscode';

export class ConfigurationManager {
    private static readonly EXTENSION_ID = 'ollama-assistant';

    getConfiguration(): vscode.WorkspaceConfiguration {
        return vscode.workspace.getConfiguration(ConfigurationManager.EXTENSION_ID);
    }

    getServerUrl(): string {
        return this.getConfiguration().get<string>('serverUrl', 'http://localhost:11434');
    }

    async setServerUrl(url: string): Promise<void> {
        await this.getConfiguration().update('serverUrl', url, vscode.ConfigurationTarget.Global);
    }

    getDefaultModel(): string {
        return this.getConfiguration().get<string>('defaultModel', '');
    }

    async setDefaultModel(model: string): Promise<void> {
        await this.getConfiguration().update('defaultModel', model, vscode.ConfigurationTarget.Global);
    }

    getChatModel(): string {
        const chatModel = this.getConfiguration().get<string>('chatModel', '');
        return chatModel || this.getDefaultModel();
    }

    async setChatModel(model: string): Promise<void> {
        await this.getConfiguration().update('chatModel', model, vscode.ConfigurationTarget.Global);
    }

    isCompletionsEnabled(): boolean {
        return this.getConfiguration().get<boolean>('enableCompletions', true);
    }

    async setCompletionsEnabled(enabled: boolean): Promise<void> {
        await this.getConfiguration().update('enableCompletions', enabled, vscode.ConfigurationTarget.Global);
    }

    getCompletionDelay(): number {
        return this.getConfiguration().get<number>('completionDelay', 500);
    }

    async setCompletionDelay(delay: number): Promise<void> {
        await this.getConfiguration().update('completionDelay', delay, vscode.ConfigurationTarget.Global);
    }

    getMaxContextLines(): number {
        return this.getConfiguration().get<number>('maxContextLines', 100);
    }

    async setMaxContextLines(lines: number): Promise<void> {
        await this.getConfiguration().update('maxContextLines', lines, vscode.ConfigurationTarget.Global);
    }

    // Listen for configuration changes
    onConfigurationChanged(callback: (e: vscode.ConfigurationChangeEvent) => void): vscode.Disposable {
        return vscode.workspace.onDidChangeConfiguration((e) => {
            if (e.affectsConfiguration(ConfigurationManager.EXTENSION_ID)) {
                callback(e);
            }
        });
    }

    // Get model options for requests
    getModelOptions() {
        return {
            temperature: 0.7,
            top_p: 0.9,
            top_k: 40,
            repeat_penalty: 1.1,
            num_ctx: 4096,
            num_predict: 512,
        };
    }

    // Get completion-specific options
    getCompletionOptions() {
        return {
            temperature: 0.2, // Lower temperature for more deterministic completions
            top_p: 0.9,
            top_k: 40,
            repeat_penalty: 1.05,
            num_ctx: 2048,
            num_predict: 128,
            stop: ['\n\n', '```', '---'], // Common stop sequences for code
        };
    }

    // Get chat-specific options
    getChatOptions() {
        return {
            temperature: 0.7,
            top_p: 0.9,
            top_k: 40,
            repeat_penalty: 1.1,
            num_ctx: 4096,
            num_predict: 1024,
        };
    }
}
