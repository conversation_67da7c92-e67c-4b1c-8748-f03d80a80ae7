import * as vscode from 'vscode';
import * as path from 'path';
import { OllamaClient } from '../services/ollamaClient';
import { TaskAgent, TaskResponse } from '../core/augmentAgent';

export class TaskChatProvider implements vscode.TreeDataProvider<TaskChatTreeItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<TaskChatTreeItem | undefined | null | void> = new vscode.EventEmitter<TaskChatTreeItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<TaskChatTreeItem | undefined | null | void> = this._onDidChangeTreeData.event;

    private chatPanel: vscode.WebviewPanel | undefined;
    private taskAgent: TaskAgent;
    private isProcessingTask = false;
    private isStreaming = false;
    private taskQueue: string[] = [];
    private currentTaskIndex = 0;

    constructor(
        private context: vscode.ExtensionContext,
        private ollamaClient: OllamaClient
    ) {
        this.taskAgent = new TaskAgent(ollamaClient, context);
    }

    getTreeItem(element: TaskChatTreeItem): vscode.TreeItem {
        return element;
    }

    getChildren(element?: TaskChatTreeItem): Thenable<TaskChatTreeItem[]> {
        if (!element) {
            return Promise.resolve([
                new TaskChatTreeItem('New Task Chat', vscode.TreeItemCollapsibleState.None, 'openTaskChat'),
                new TaskChatTreeItem('Clear Task History', vscode.TreeItemCollapsibleState.None, 'clearTaskHistory')
            ]);
        }
        return Promise.resolve([]);
    }

    getTreeDataProvider(): vscode.TreeDataProvider<TaskChatTreeItem> {
        return this;
    }

    async openTaskChat(): Promise<void> {
        if (this.chatPanel) {
            // If a panel already exists, don't create a new one.
            // Instead, just reveal the existing one and return.
            this.chatPanel.reveal(vscode.ViewColumn.Beside);
            return;
        }

        this.chatPanel = vscode.window.createWebviewPanel(
            'task-chat',
            'AI Task Agent',
            vscode.ViewColumn.Beside,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [
                    vscode.Uri.joinPath(this.context.extensionUri, 'media'),
                    vscode.Uri.joinPath(this.context.extensionUri, 'out')
                ]
            }
        );

        this.chatPanel.webview.html = this.getWebviewContent();

        // Handle messages from webview
        this.chatPanel.webview.onDidReceiveMessage(
            async (message) => {
                switch (message.type) {
                    case 'startTask':
                        await this.handleUserPrompt(message.text, message.includeContext);
                        break;
                    case 'stopTask':
                        await this.stopTaskExecution();
                        break;
                    case 'clearTaskHistory':
                        await this.handleClearTaskHistory();
                        break;
                    case 'getModels':
                        await this.handleGetModels();
                        break;
                    case 'selectModel':
                        await this.handleSelectModel(message.model);
                        break;
                }
            },
            undefined,
            this.context.subscriptions
        );

        this.chatPanel.onDidDispose(() => {
            this.chatPanel = undefined;
        });

        // Send initial data
        await this.sendInitialData();
    }

    private async sendInitialData(): Promise<void> {
        if (!this.chatPanel) {
            return;
        }

        // Send task history
        this.chatPanel.webview.postMessage({
            type: 'taskHistory',
            history: this.taskAgent.getTaskHistory()
        });

        // Send current task status
        this.chatPanel.webview.postMessage({
            type: 'taskStatus',
            hasNextTask: this.taskAgent.hasNextTask(),
            currentTask: this.taskAgent.getCurrentTask()
        });

        // Send available models
        try {
            const models = this.ollamaClient.getAvailableModels();
            this.chatPanel.webview.postMessage({
                type: 'availableModels',
                models: models.map(m => m.name)
            });
        } catch (error) {
            console.error('Error getting models:', error);
        }
    }

    // Main entry point for user prompts - starts the automated task execution
    private async handleUserPrompt(userPrompt: string, includeContext: boolean): Promise<void> {
        if (!this.chatPanel || !userPrompt.trim() || this.isProcessingTask) {
            return;
        }

        // Send user message to webview
        this.chatPanel.webview.postMessage({
            type: 'userMessage',
            message: userPrompt
        });

        // Start the automated task execution loop
        await this.executeTask(userPrompt, includeContext);
    }

    // Core automated task execution system with feedback loop
    private async executeTask(prompt: string, includeContext: boolean = true): Promise<void> {
        if (!this.chatPanel) {
            return;
        }

        try {
            // 1. Show UI Spinner (start of process)
            this.setStreamingStatus(true);
            this.chatPanel.webview.postMessage({
                type: 'taskProcessingStart',
                message: 'Processing task...'
            });

            // 2. Get updated context for each step
            const activeEditor = vscode.window.activeTextEditor;

            // 3. Process the task with AI
            const taskResponse = await this.taskAgent.processTask(
                prompt,
                includeContext,
                activeEditor?.document,
                activeEditor?.selection
            );

            // 4. Update the UI with a description of the task to be executed
            this.chatPanel.webview.postMessage({
                type: 'addMessage',
                isSystem: true,
                text: `🎯 Task: ${taskResponse.task_description}`,
                taskResponse: taskResponse
            });

            // 5. Execute the command automatically
            await this.executeAction(taskResponse);

            // 6. Show completion message
            this.chatPanel.webview.postMessage({
                type: 'addMessage',
                isSystem: true,
                text: `✅ Completed: ${taskResponse.task_description}`
            });

            // 7. Check for the next task and continue the loop
            if (taskResponse.next_task !== null && taskResponse.next_task.trim() !== '') {
                // Small delay makes the UI feel more natural and allows user to see progress
                setTimeout(() => {
                    if (this.isProcessingTask) { // Check if user hasn't stopped the process
                        this.executeTask(taskResponse.next_task!, includeContext);
                    }
                }, 1000);
            } else {
                // All tasks are complete
                this.chatPanel.webview.postMessage({
                    type: 'addMessage',
                    isSystem: true,
                    text: '🎉 All tasks completed successfully!'
                });
                this.setStreamingStatus(false);
                this.isProcessingTask = false;
            }

        } catch (error) {
            console.error('Task execution error:', error);

            // Handle parsing errors or invalid JSON
            this.chatPanel.webview.postMessage({
                type: 'addMessage',
                isSystem: true,
                text: `❌ Error: ${error}`
            });

            // Try to recover by asking AI to fix the response
            const recoveryPrompt = `The previous response was invalid or caused an error: ${error}. Please provide only a single, valid JSON object for the task: ${prompt}`;

            setTimeout(() => {
                if (this.isProcessingTask) {
                    this.executeTask(recoveryPrompt, includeContext);
                }
            }, 1000);
        }
    }

    // Streaming status management for UI feedback
    private setStreamingStatus(status: boolean): void {
        this.isStreaming = status;
        this.isProcessingTask = status;

        if (this.chatPanel) {
            this.chatPanel.webview.postMessage({
                type: 'setStreamingStatus',
                status: status
            });
        }
    }

    // Stop task execution
    private async stopTaskExecution(): Promise<void> {
        this.isProcessingTask = false;
        this.setStreamingStatus(false);

        if (this.chatPanel) {
            this.chatPanel.webview.postMessage({
                type: 'addMessage',
                isSystem: true,
                text: '⏹️ Task execution stopped by user'
            });
        }
    }

    // Execute the action from TaskResponse automatically
    private async executeAction(taskResponse: TaskResponse): Promise<void> {
        try {
            switch (taskResponse.action) {
                case 'create_file':
                    await this.executeCreateFile(taskResponse);
                    break;
                case 'modify_file':
                    await this.executeModifyFile(taskResponse);
                    break;
                case 'delete_file':
                    await this.executeDeleteFile(taskResponse);
                    break;
                case 'run_command':
                    await this.executeRunCommand(taskResponse);
                    break;
                case 'chat':
                    // For chat responses, just display the message
                    if (this.chatPanel && taskResponse.message) {
                        this.chatPanel.webview.postMessage({
                            type: 'addMessage',
                            isSystem: false,
                            text: taskResponse.message
                        });
                    }
                    break;
                default:
                    console.warn(`Unknown action type: ${taskResponse.action}`);
            }

        } catch (error) {
            console.error('Error executing action:', error);
            throw error; // Re-throw to be handled by executeTask
        }
    }

    private async executeCreateFile(taskResponse: TaskResponse): Promise<void> {
        if (!taskResponse.file_path || !taskResponse.content) {
            throw new Error('Missing file path or content for create_file action');
        }

        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder open');
        }

        const filePath = vscode.Uri.joinPath(workspaceFolder.uri, taskResponse.file_path);
        await vscode.workspace.fs.writeFile(filePath, Buffer.from(taskResponse.content, 'utf8'));
        
        // Open the created file
        const document = await vscode.workspace.openTextDocument(filePath);
        await vscode.window.showTextDocument(document);
    }

    private async executeModifyFile(taskResponse: TaskResponse): Promise<void> {
        if (!taskResponse.file_path || !taskResponse.content) {
            throw new Error('Missing file path or content for modify_file action');
        }

        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder open');
        }

        const filePath = vscode.Uri.joinPath(workspaceFolder.uri, taskResponse.file_path);
        await vscode.workspace.fs.writeFile(filePath, Buffer.from(taskResponse.content, 'utf8'));
        
        // Open the modified file
        const document = await vscode.workspace.openTextDocument(filePath);
        await vscode.window.showTextDocument(document);
    }

    private async executeDeleteFile(taskResponse: TaskResponse): Promise<void> {
        if (!taskResponse.file_path) {
            throw new Error('Missing file path for delete_file action');
        }

        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder open');
        }

        const filePath = vscode.Uri.joinPath(workspaceFolder.uri, taskResponse.file_path);
        await vscode.workspace.fs.delete(filePath);
    }

    private async executeRunCommand(taskResponse: TaskResponse): Promise<void> {
        if (!taskResponse.command) {
            throw new Error('Missing command for run_command action');
        }

        // Create a new terminal and run the command
        const terminal = vscode.window.createTerminal('AI Task Agent');
        terminal.sendText(taskResponse.command);
        terminal.show();
    }

    private async handleClearTaskHistory(): Promise<void> {
        this.taskAgent.clearTaskHistory();
        if (this.chatPanel) {
            this.chatPanel.webview.postMessage({
                type: 'taskHistoryCleared'
            });
            this.chatPanel.webview.postMessage({
                type: 'taskStatus',
                hasNextTask: false,
                currentTask: null
            });
        }
    }

    private async handleGetModels(): Promise<void> {
        try {
            await this.ollamaClient.refreshModels();
            const models = this.ollamaClient.getAvailableModels();
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'availableModels',
                    models: models.map(m => m.name)
                });
            }
        } catch (error) {
            console.error('Error getting models:', error);
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'error',
                    message: 'Failed to load models'
                });
            }
        }
    }

    private async handleSelectModel(model: string): Promise<void> {
        try {
            await vscode.workspace.getConfiguration('ollama-assistant').update('chatModel', model, vscode.ConfigurationTarget.Global);
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'modelSelected',
                    model
                });
            }
        } catch (error) {
            console.error('Error selecting model:', error);
        }
    }

    private getWebviewContent(): string {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Task Agent</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 20px;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--vscode-panel-border);
        }

        .header-title {
            font-size: 1.2em;
            font-weight: bold;
            color: var(--vscode-textLink-foreground);
        }

        .model-selector {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 10px;
        }

        .task-status {
            background-color: var(--vscode-badge-background);
            color: var(--vscode-badge-foreground);
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .task-status.processing {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            animation: pulse 1.5s ease-in-out infinite alternate;
        }

        @keyframes pulse {
            from { opacity: 0.6; }
            to { opacity: 1.0; }
        }

        .stop-button {
            background-color: var(--vscode-errorForeground);
            color: var(--vscode-editor-background);
        }

        .stop-button:hover {
            background-color: var(--vscode-errorForeground);
            opacity: 0.8;
        }

        select {
            background-color: var(--vscode-dropdown-background);
            color: var(--vscode-dropdown-foreground);
            border: 1px solid var(--vscode-dropdown-border);
            padding: 8px;
            border-radius: 3px;
            min-width: 200px;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
            border: 1px solid var(--vscode-panel-border);
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .message {
            margin-bottom: 15px;
            padding: 12px;
            border-radius: 6px;
        }

        .user-message {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            margin-left: 20%;
        }

        .system-message {
            background-color: var(--vscode-editor-selectionBackground);
            margin: 10px 0;
            padding: 12px;
            border-radius: 6px;
            border-left: 4px solid var(--vscode-textLink-foreground);
            font-family: var(--vscode-editor-font-family);
        }

        .system-message.success {
            border-left-color: var(--vscode-terminal-ansiGreen);
        }

        .system-message.error {
            border-left-color: var(--vscode-errorForeground);
            background-color: var(--vscode-inputValidation-errorBackground);
        }

        .system-message.task {
            border-left-color: var(--vscode-textLink-foreground);
        }

        .spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid var(--vscode-progressBar-background);
            border-radius: 50%;
            border-top-color: var(--vscode-progressBar-foreground);
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-family: inherit;
            font-size: 13px;
        }

        button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }

        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .execute-btn {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
        }

        .execute-btn:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);
        }

        .continue-btn {
            background-color: var(--vscode-textLink-foreground);
            color: var(--vscode-editor-background);
        }

        .input-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
            padding: 10px;
            border-top: 1px solid var(--vscode-panel-border);
            background-color: var(--vscode-editor-background);
        }

        textarea {
            width: 100%;
            min-height: 80px;
            max-height: 150px;
            padding: 12px;
            border: 1px solid var(--vscode-input-border);
            border-radius: 5px;
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            font-family: inherit;
            font-size: inherit;
            resize: vertical;
            box-sizing: border-box;
        }

        .input-options {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 10px;
        }

        .processing {
            color: var(--vscode-descriptionForeground);
            font-style: italic;
            text-align: center;
            padding: 20px;
        }

        .error {
            color: var(--vscode-errorForeground);
            background-color: var(--vscode-inputValidation-errorBackground);
            border: 1px solid var(--vscode-inputValidation-errorBorder);
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
        }

        .success {
            color: var(--vscode-terminal-ansiGreen);
            background-color: var(--vscode-inputValidation-infoBackground);
            border: 1px solid var(--vscode-inputValidation-infoBorder);
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
        }

        pre {
            background-color: var(--vscode-textCodeBlock-background);
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            white-space: pre-wrap;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-title">🤖 AI Task Agent</div>
        <div class="model-selector">
            <label for="model-select">Model:</label>
            <select id="model-select">
                <option value="">Select a model...</option>
            </select>
            <button onclick="refreshModels()">Refresh</button>
            <button onclick="clearTaskHistory()">Clear History</button>
        </div>
        <div id="task-status" class="task-status">No active task</div>
    </div>

    <div class="chat-container">
        <div id="messages" class="messages"></div>

        <div class="input-container">
            <div class="input-options">
                <label>
                    <input type="checkbox" id="include-context" checked>
                    Include current file context
                </label>
                <button id="stop-btn" onclick="stopTask()" disabled class="stop-button">
                    ⏹️ Stop Execution
                </button>
            </div>
            <textarea id="message-input" placeholder="Describe the complex task you want me to complete automatically..."></textarea>
            <button onclick="startTask()" id="start-button">🚀 Start Automated Task</button>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        let isProcessing = false;

        // Handle messages from extension
        window.addEventListener('message', event => {
            const message = event.data;
            console.log('Webview received message:', message);

            switch (message.type) {
                case 'setStreamingStatus':
                    setStreamingStatus(message.status);
                    break;
                case 'addMessage':
                    addMessage(message.text, message.isSystem);
                    break;
                case 'availableModels':
                    updateModelSelector(message.models);
                    break;
                case 'userMessage':
                    displayUserMessage(message.message);
                    break;
                case 'taskProcessingStart':
                    showProcessing(true, message.message);
                    break;
                case 'taskProcessingEnd':
                    showProcessing(false);
                    break;
                case 'error':
                    displayError(message.message);
                    break;
                case 'taskHistoryCleared':
                    clearMessages();
                    break;
                case 'modelSelected':
                    document.getElementById('model-select').value = message.model;
                    break;
            }
        });

        function startTask() {
            const input = document.getElementById('message-input');
            const includeContext = document.getElementById('include-context').checked;
            const text = input.value.trim();

            if (!text || isProcessing) return;

            vscode.postMessage({
                type: 'startTask',
                text: text,
                includeContext: includeContext
            });

            input.value = '';
        }

        function stopTask() {
            vscode.postMessage({
                type: 'stopTask'
            });
        }

        function setStreamingStatus(status) {
            isProcessing = status;
            const startButton = document.getElementById('start-button');
            const stopButton = document.getElementById('stop-btn');
            const statusDiv = document.getElementById('task-status');

            startButton.disabled = status;
            stopButton.disabled = !status;

            if (status) {
                statusDiv.textContent = '🤖 Processing tasks automatically...';
                statusDiv.className = 'task-status processing';
            } else {
                statusDiv.textContent = 'Ready for new task';
                statusDiv.className = 'task-status';
            }
        }

        function addMessage(text, isSystem) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');

            if (isSystem) {
                messageDiv.className = 'system-message';
                if (text.includes('✅')) {
                    messageDiv.className += ' success';
                } else if (text.includes('❌')) {
                    messageDiv.className += ' error';
                } else if (text.includes('🎯')) {
                    messageDiv.className += ' task';
                }
            } else {
                messageDiv.className = 'message user-message';
            }

            messageDiv.textContent = text;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function clearTaskHistory() {
            vscode.postMessage({ type: 'clearTaskHistory' });
        }

        function refreshModels() {
            vscode.postMessage({ type: 'getModels' });
        }

        function displayTaskHistory(history) {
            const messagesDiv = document.getElementById('messages');
            messagesDiv.innerHTML = '';

            history.forEach(item => {
                displayUserMessage(item.task);
                displayTaskResponse(item.response);
            });
        }

        function displayUserMessage(message) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message user-message';
            messageDiv.innerHTML = '<strong>🧑‍💻 User:</strong> ' + escapeHtml(message);
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function showProcessing(processing, message) {
            if (processing) {
                const messagesDiv = document.getElementById('messages');
                const processingDiv = document.createElement('div');
                processingDiv.className = 'system-message';
                processingDiv.id = 'processing-indicator';
                processingDiv.innerHTML = '<span class="spinner"></span>' + (message || 'Processing...');
                messagesDiv.appendChild(processingDiv);
                messagesDiv.scrollTop = messagesDiv.scrollHeight;
            } else {
                const processingDiv = document.getElementById('processing-indicator');
                if (processingDiv) {
                    processingDiv.remove();
                }
            }
        }

        function displayError(message) {
            const messagesDiv = document.getElementById('messages');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'system-message error';
            errorDiv.textContent = '❌ Error: ' + message;
            messagesDiv.appendChild(errorDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
            setStreamingStatus(false);
        }

        function updateModelSelector(models) {
            const select = document.getElementById('model-select');
            const currentValue = select.value;

            select.innerHTML = '<option value="">Select a model...</option>';
            models.forEach(model => {
                const option = document.createElement('option');
                option.value = model;
                option.textContent = model;
                select.appendChild(option);
            });

            if (models.includes(currentValue)) {
                select.value = currentValue;
            }
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Handle Enter key in textarea
        document.getElementById('message-input').addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                startTask();
            }
        });

        // Handle model selection
        document.getElementById('model-select').addEventListener('change', function(e) {
            if (e.target.value) {
                vscode.postMessage({
                    type: 'selectModel',
                    model: e.target.value
                });
            }
        });

        // Request initial data
        vscode.postMessage({ type: 'getModels' });
    </script>
</body>
</html>`;
    }

    dispose(): void {
        if (this.chatPanel) {
            this.chatPanel.dispose();
        }
    }
}

class TaskChatTreeItem extends vscode.TreeItem {
    constructor(
        public readonly label: string,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState,
        public readonly commandId?: string
    ) {
        super(label, collapsibleState);

        if (commandId) {
            this.command = {
                command: `ollama-assistant.${commandId}`,
                title: label
            };
        }
    }
}
