import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';

export interface WorkspaceContext {
    rootPath: string;
    fileCount: number;
    languages: string[];
    structure: FileNode[];
    dependencies: Dependency[];
    gitInfo?: GitInfo;
}

export interface FileNode {
    name: string;
    path: string;
    type: 'file' | 'directory';
    language?: string;
    size?: number;
    children?: FileNode[];
}

export interface Dependency {
    name: string;
    version: string;
    type: 'production' | 'development';
    source: 'npm' | 'pip' | 'cargo' | 'maven' | 'other';
}

export interface GitInfo {
    branch: string;
    commit: string;
    status: string;
    remotes: string[];
}

export interface FileContext {
    path: string;
    language: string;
    content: string;
    lineCount: number;
    selection?: {
        text: string;
        startLine: number;
        endLine: number;
    };
    imports: string[];
    exports: string[];
    functions: FunctionInfo[];
    classes: ClassInfo[];
    variables: VariableInfo[];
}

export interface FunctionInfo {
    name: string;
    line: number;
    parameters: string[];
    returnType?: string;
    docstring?: string;
}

export interface ClassInfo {
    name: string;
    line: number;
    methods: FunctionInfo[];
    properties: VariableInfo[];
    extends?: string;
    implements?: string[];
}

export interface VariableInfo {
    name: string;
    line: number;
    type?: string;
    scope: 'global' | 'local' | 'class' | 'function';
}

export interface RelatedFile {
    path: string;
    relationship: 'import' | 'export' | 'reference' | 'similar' | 'test';
    confidence: number;
}

export interface ComprehensiveContext {
    workspace: WorkspaceContext;
    activeFile?: FileContext;
    relatedFiles: RelatedFile[];
    dependencies: Dependency[];
    recentChanges: string[];
    codePatterns: string[];
    contextSize: number;
}

export class AdvancedContextEngine {
    private contextCache = new Map<string, any>();
    private maxContextSize = 200000; // 200K context like AugmentCode

    async gatherComprehensiveContext(
        activeFile?: vscode.TextDocument,
        selection?: vscode.Selection
    ): Promise<ComprehensiveContext> {
        const workspace = await this.analyzeWorkspace();
        const activeFileContext = activeFile ? await this.analyzeFile(activeFile, selection) : undefined;
        const relatedFiles = activeFileContext ? await this.findRelatedFiles(activeFileContext) : [];
        const dependencies = await this.analyzeDependencies();
        const recentChanges = await this.getRecentChanges();
        const codePatterns = await this.extractCodePatterns(activeFileContext);

        const context: ComprehensiveContext = {
            workspace,
            activeFile: activeFileContext,
            relatedFiles,
            dependencies,
            recentChanges,
            codePatterns,
            contextSize: 0
        };

        // Calculate and optimize context size
        context.contextSize = this.calculateContextSize(context);
        if (context.contextSize > this.maxContextSize) {
            await this.optimizeContext(context);
        }

        return context;
    }

    private async analyzeWorkspace(): Promise<WorkspaceContext> {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder open');
        }

        const cacheKey = `workspace_${workspaceFolder.uri.fsPath}`;
        if (this.contextCache.has(cacheKey)) {
            return this.contextCache.get(cacheKey);
        }

        const rootPath = workspaceFolder.uri.fsPath;
        const structure = await this.buildFileStructure(workspaceFolder.uri);
        const languages = this.extractLanguages(structure);
        const fileCount = this.countFiles(structure);
        const dependencies = await this.analyzeDependencies();
        const gitInfo = await this.getGitInfo(rootPath);

        const workspace: WorkspaceContext = {
            rootPath,
            fileCount,
            languages,
            structure,
            dependencies,
            gitInfo
        };

        this.contextCache.set(cacheKey, workspace);
        return workspace;
    }

    private async buildFileStructure(uri: vscode.Uri, maxDepth: number = 3, currentDepth: number = 0): Promise<FileNode[]> {
        if (currentDepth >= maxDepth) return [];

        try {
            const entries = await vscode.workspace.fs.readDirectory(uri);
            const nodes: FileNode[] = [];

            for (const [name, type] of entries) {
                // Skip common ignore patterns
                if (this.shouldIgnoreFile(name)) continue;

                const childUri = vscode.Uri.joinPath(uri, name);
                const relativePath = vscode.workspace.asRelativePath(childUri);

                const node: FileNode = {
                    name,
                    path: relativePath,
                    type: type === vscode.FileType.Directory ? 'directory' : 'file'
                };

                if (type === vscode.FileType.File) {
                    node.language = this.getLanguageFromExtension(name);
                    try {
                        const stat = await vscode.workspace.fs.stat(childUri);
                        node.size = stat.size;
                    } catch (error) {
                        // Ignore stat errors
                    }
                } else if (type === vscode.FileType.Directory) {
                    node.children = await this.buildFileStructure(childUri, maxDepth, currentDepth + 1);
                }

                nodes.push(node);
            }

            return nodes.sort((a, b) => {
                if (a.type !== b.type) {
                    return a.type === 'directory' ? -1 : 1;
                }
                return a.name.localeCompare(b.name);
            });
        } catch (error) {
            console.error('Error building file structure:', error);
            return [];
        }
    }

    private shouldIgnoreFile(name: string): boolean {
        const ignorePatterns = [
            'node_modules', '.git', '.vscode', 'dist', 'build', 'out',
            '.DS_Store', 'Thumbs.db', '*.log', '.env', '.env.local',
            '__pycache__', '*.pyc', '.pytest_cache', 'venv', 'env',
            'target', 'bin', 'obj', '.vs', '.idea'
        ];

        return ignorePatterns.some(pattern => {
            if (pattern.includes('*')) {
                const regex = new RegExp(pattern.replace('*', '.*'));
                return regex.test(name);
            }
            return name === pattern || name.startsWith(pattern);
        });
    }

    private getLanguageFromExtension(filename: string): string {
        const ext = path.extname(filename).toLowerCase();
        const languageMap: { [key: string]: string } = {
            '.js': 'javascript',
            '.ts': 'typescript',
            '.jsx': 'javascriptreact',
            '.tsx': 'typescriptreact',
            '.py': 'python',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.cs': 'csharp',
            '.php': 'php',
            '.rb': 'ruby',
            '.go': 'go',
            '.rs': 'rust',
            '.swift': 'swift',
            '.kt': 'kotlin',
            '.scala': 'scala',
            '.html': 'html',
            '.css': 'css',
            '.scss': 'scss',
            '.sass': 'sass',
            '.less': 'less',
            '.json': 'json',
            '.xml': 'xml',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.md': 'markdown',
            '.sql': 'sql',
            '.sh': 'shellscript',
            '.ps1': 'powershell',
            '.dockerfile': 'dockerfile'
        };

        return languageMap[ext] || 'plaintext';
    }

    private extractLanguages(structure: FileNode[]): string[] {
        const languages = new Set<string>();
        
        const traverse = (nodes: FileNode[]) => {
            for (const node of nodes) {
                if (node.type === 'file' && node.language) {
                    languages.add(node.language);
                }
                if (node.children) {
                    traverse(node.children);
                }
            }
        };

        traverse(structure);
        return Array.from(languages);
    }

    private countFiles(structure: FileNode[]): number {
        let count = 0;
        
        const traverse = (nodes: FileNode[]) => {
            for (const node of nodes) {
                if (node.type === 'file') {
                    count++;
                }
                if (node.children) {
                    traverse(node.children);
                }
            }
        };

        traverse(structure);
        return count;
    }

    private async analyzeFile(document: vscode.TextDocument, selection?: vscode.Selection): Promise<FileContext> {
        const content = document.getText();
        const language = document.languageId;
        const lineCount = document.lineCount;
        
        const fileContext: FileContext = {
            path: vscode.workspace.asRelativePath(document.uri),
            language,
            content,
            lineCount,
            imports: [],
            exports: [],
            functions: [],
            classes: [],
            variables: []
        };

        if (selection && !selection.isEmpty) {
            fileContext.selection = {
                text: document.getText(selection),
                startLine: selection.start.line + 1,
                endLine: selection.end.line + 1
            };
        }

        // Analyze code structure based on language
        await this.analyzeCodeStructure(fileContext);

        return fileContext;
    }

    private async analyzeCodeStructure(fileContext: FileContext): Promise<void> {
        const { content, language } = fileContext;
        const lines = content.split('\n');

        switch (language) {
            case 'javascript':
            case 'typescript':
            case 'javascriptreact':
            case 'typescriptreact':
                this.analyzeJavaScriptStructure(fileContext, lines);
                break;
            case 'python':
                this.analyzePythonStructure(fileContext, lines);
                break;
            case 'java':
                this.analyzeJavaStructure(fileContext, lines);
                break;
            default:
                this.analyzeGenericStructure(fileContext, lines);
        }
    }

    private analyzeJavaScriptStructure(fileContext: FileContext, lines: string[]): void {
        lines.forEach((line, index) => {
            const trimmed = line.trim();
            
            // Imports
            if (trimmed.startsWith('import ') || trimmed.startsWith('const ') && trimmed.includes('require(')) {
                const match = trimmed.match(/from ['"]([^'"]+)['"]|require\(['"]([^'"]+)['"]\)/);
                if (match) {
                    fileContext.imports.push(match[1] || match[2]);
                }
            }
            
            // Exports
            if (trimmed.startsWith('export ')) {
                fileContext.exports.push(trimmed);
            }
            
            // Functions
            const funcMatch = trimmed.match(/(?:function\s+(\w+)|const\s+(\w+)\s*=.*?(?:function|\(.*?\)\s*=>))/);
            if (funcMatch) {
                fileContext.functions.push({
                    name: funcMatch[1] || funcMatch[2],
                    line: index + 1,
                    parameters: []
                });
            }
            
            // Classes
            const classMatch = trimmed.match(/class\s+(\w+)/);
            if (classMatch) {
                fileContext.classes.push({
                    name: classMatch[1],
                    line: index + 1,
                    methods: [],
                    properties: []
                });
            }
        });
    }

    private analyzePythonStructure(fileContext: FileContext, lines: string[]): void {
        lines.forEach((line, index) => {
            const trimmed = line.trim();
            
            // Imports
            if (trimmed.startsWith('import ') || trimmed.startsWith('from ')) {
                fileContext.imports.push(trimmed);
            }
            
            // Functions
            const funcMatch = trimmed.match(/def\s+(\w+)\s*\(/);
            if (funcMatch) {
                fileContext.functions.push({
                    name: funcMatch[1],
                    line: index + 1,
                    parameters: []
                });
            }
            
            // Classes
            const classMatch = trimmed.match(/class\s+(\w+)/);
            if (classMatch) {
                fileContext.classes.push({
                    name: classMatch[1],
                    line: index + 1,
                    methods: [],
                    properties: []
                });
            }
        });
    }

    private analyzeJavaStructure(fileContext: FileContext, lines: string[]): void {
        // Similar analysis for Java
        // Implementation details...
    }

    private analyzeGenericStructure(fileContext: FileContext, lines: string[]): void {
        // Generic analysis for other languages
        // Implementation details...
    }

    private async findRelatedFiles(fileContext: FileContext): Promise<RelatedFile[]> {
        const relatedFiles: RelatedFile[] = [];
        
        // Find files based on imports
        for (const importPath of fileContext.imports) {
            const resolvedPath = await this.resolveImportPath(importPath, fileContext.path);
            if (resolvedPath) {
                relatedFiles.push({
                    path: resolvedPath,
                    relationship: 'import',
                    confidence: 0.9
                });
            }
        }

        // Find test files
        const testFile = await this.findTestFile(fileContext.path);
        if (testFile) {
            relatedFiles.push({
                path: testFile,
                relationship: 'test',
                confidence: 0.8
            });
        }

        return relatedFiles;
    }

    private async resolveImportPath(importPath: string, currentFile: string): Promise<string | null> {
        // Implementation for resolving import paths
        // This would handle relative imports, node_modules, etc.
        return null;
    }

    private async findTestFile(filePath: string): Promise<string | null> {
        // Implementation for finding corresponding test files
        return null;
    }

    private async analyzeDependencies(): Promise<Dependency[]> {
        const dependencies: Dependency[] = [];
        
        // Check package.json
        const packageJsonPath = vscode.Uri.joinPath(vscode.workspace.workspaceFolders![0].uri, 'package.json');
        try {
            const packageJsonContent = await vscode.workspace.fs.readFile(packageJsonPath);
            const packageJson = JSON.parse(packageJsonContent.toString());
            
            if (packageJson.dependencies) {
                for (const [name, version] of Object.entries(packageJson.dependencies)) {
                    dependencies.push({
                        name,
                        version: version as string,
                        type: 'production',
                        source: 'npm'
                    });
                }
            }
            
            if (packageJson.devDependencies) {
                for (const [name, version] of Object.entries(packageJson.devDependencies)) {
                    dependencies.push({
                        name,
                        version: version as string,
                        type: 'development',
                        source: 'npm'
                    });
                }
            }
        } catch (error) {
            // No package.json or error reading it
        }

        return dependencies;
    }

    private async getGitInfo(rootPath: string): Promise<GitInfo | undefined> {
        // Implementation for getting Git information
        return undefined;
    }

    private async getRecentChanges(): Promise<string[]> {
        // Implementation for getting recent file changes
        return [];
    }

    private async extractCodePatterns(fileContext?: FileContext): Promise<string[]> {
        // Implementation for extracting code patterns
        return [];
    }

    private calculateContextSize(context: ComprehensiveContext): number {
        let size = 0;
        
        if (context.activeFile) {
            size += context.activeFile.content.length;
        }
        
        // Add size calculations for other context elements
        
        return size;
    }

    private async optimizeContext(context: ComprehensiveContext): Promise<void> {
        // Implementation for optimizing context when it exceeds limits
        // This would prioritize the most relevant information
    }
}
